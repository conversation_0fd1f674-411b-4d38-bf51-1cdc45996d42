"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SafeAreaView = exports.InputAccessoryView = exports.TouchableNativeFeedback = exports.DrawerLayoutAndroid = exports.VirtualizedList = exports.View = exports.TouchableWithoutFeedback = exports.TouchableOpacity = exports.TouchableHighlight = exports.TextInput = exports.Text = exports.Switch = exports.SectionList = exports.ScrollView = exports.RefreshControl = exports.Pressable = exports.Modal = exports.KeyboardAvoidingView = exports.ImageBackground = exports.Image = exports.FlatList = exports.ActivityIndicator = void 0;
const styled_1 = require("./styled");
const react_native_1 = require("react-native");
/**
 * Hello!
 *
 * These are undocumented pre-styled components.
 *
 * The reason why they are undocumented, is that they will eventually have
 * base styles, similar to Tailwind Preflight.
 *
 * We're still undecided on what this should be, but if you'd like to start please
 * open a PR or a Github discussion
 */
exports.ActivityIndicator = (0, styled_1.styled)(react_native_1.ActivityIndicator);
exports.FlatList = (0, styled_1.styled)(react_native_1.FlatList);
exports.Image = (0, styled_1.styled)(react_native_1.Image);
exports.ImageBackground = (0, styled_1.styled)(react_native_1.ImageBackground);
exports.KeyboardAvoidingView = (0, styled_1.styled)(react_native_1.KeyboardAvoidingView);
exports.Modal = (0, styled_1.styled)(react_native_1.Modal);
exports.Pressable = (0, styled_1.styled)(react_native_1.Pressable);
exports.RefreshControl = (0, styled_1.styled)(react_native_1.RefreshControl);
exports.ScrollView = (0, styled_1.styled)(react_native_1.ScrollView);
exports.SectionList = (0, styled_1.styled)(react_native_1.SectionList);
exports.Switch = (0, styled_1.styled)(react_native_1.Switch);
exports.Text = (0, styled_1.styled)(react_native_1.Text);
exports.TextInput = (0, styled_1.styled)(react_native_1.TextInput);
exports.TouchableHighlight = (0, styled_1.styled)(react_native_1.TouchableHighlight);
exports.TouchableOpacity = (0, styled_1.styled)(react_native_1.TouchableOpacity);
exports.TouchableWithoutFeedback = (0, styled_1.styled)(react_native_1.TouchableWithoutFeedback);
exports.View = (0, styled_1.styled)(react_native_1.View);
exports.VirtualizedList = (0, styled_1.styled)(react_native_1.VirtualizedList);
exports.DrawerLayoutAndroid = (0, styled_1.styled)(react_native_1.DrawerLayoutAndroid);
exports.TouchableNativeFeedback = (0, styled_1.styled)(react_native_1.TouchableNativeFeedback);
exports.InputAccessoryView = (0, styled_1.styled)(react_native_1.InputAccessoryView);
exports.SafeAreaView = (0, styled_1.styled)(react_native_1.SafeAreaView);
