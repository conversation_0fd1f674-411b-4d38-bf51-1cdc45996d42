{"version": 3, "names": ["toIdentifier", "input", "name", "c", "isIdentifierChar", "codePointAt", "replace", "match", "toUpperCase", "isValidIdentifier"], "sources": ["../../src/converters/toIdentifier.ts"], "sourcesContent": ["import isValidIdentifier from \"../validators/isValidIdentifier\";\nimport { isIdentifierChar } from \"@babel/helper-validator-identifier\";\n\nexport default function toIdentifier(input: string): string {\n  input = input + \"\";\n\n  // replace all non-valid identifiers with dashes\n  let name = \"\";\n  for (const c of input) {\n    name += isIdentifierChar(c.codePointAt(0)) ? c : \"-\";\n  }\n\n  // remove all dashes and numbers from start of name\n  name = name.replace(/^[-0-9]+/, \"\");\n\n  // camel case\n  name = name.replace(/[-\\s]+(.)?/g, function (match, c) {\n    return c ? c.toUpperCase() : \"\";\n  });\n\n  if (!isValidIdentifier(name)) {\n    name = `_${name}`;\n  }\n\n  return name || \"_\";\n}\n"], "mappings": ";;;;;;;AAAA;;AACA;;AAEe,SAASA,YAAT,CAAsBC,KAAtB,EAA6C;EAC1DA,KAAK,GAAGA,KAAK,GAAG,EAAhB;EAGA,IAAIC,IAAI,GAAG,EAAX;;EACA,KAAK,MAAMC,CAAX,IAAgBF,KAAhB,EAAuB;IACrBC,IAAI,IAAI,IAAAE,2CAAA,EAAiBD,CAAC,CAACE,WAAF,CAAc,CAAd,CAAjB,IAAqCF,CAArC,GAAyC,GAAjD;EACD;;EAGDD,IAAI,GAAGA,IAAI,CAACI,OAAL,CAAa,UAAb,EAAyB,EAAzB,CAAP;EAGAJ,IAAI,GAAGA,IAAI,CAACI,OAAL,CAAa,aAAb,EAA4B,UAAUC,KAAV,EAAiBJ,CAAjB,EAAoB;IACrD,OAAOA,CAAC,GAAGA,CAAC,CAACK,WAAF,EAAH,GAAqB,EAA7B;EACD,CAFM,CAAP;;EAIA,IAAI,CAAC,IAAAC,0BAAA,EAAkBP,IAAlB,CAAL,EAA8B;IAC5BA,IAAI,GAAI,IAAGA,IAAK,EAAhB;EACD;;EAED,OAAOA,IAAI,IAAI,GAAf;AACD"}