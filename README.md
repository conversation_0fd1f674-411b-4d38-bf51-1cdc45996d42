# Legal Buddy - Indian Law AI Assistant 🏛️⚖️

A sophisticated React Native app that provides AI-powered legal guidance specifically for Indian law, including the Indian Constitution, IPC, CrPC, and other legal frameworks.

## ✨ Features

### 🎯 **Advanced AI Legal Assistant**
- **Indian Law Expert**: Specialized knowledge of Indian Constitution, IPC, CrPC, CPC
- **Smart Question Recognition**: Automatically categorizes questions by law type
- **Detailed Responses**: Includes relevant sections, articles, and case law
- **Quick Questions**: Pre-built popular legal questions for easy access

### 🎨 **Premium UI Design**
- **Modern Interface**: Professional, app-store ready design
- **Interactive Elements**: Quick question buttons, smart input validation
- **Beautiful Cards**: Elegant response cards with proper typography
- **Smooth Animations**: Loading states and touch feedback
- **Cross-Platform**: Works on iOS, Android, and Web

### 📚 **Smart Journal System**
- **Save Responses**: Keep important legal information for reference
- **Categorized Entries**: Automatic tagging by law type
- **Search & Filter**: Easy access to saved information
- **Export Options**: Share or backup your legal research

## 🚀 Getting Started

### Prerequisites
- Node.js (v16 or higher)
- Expo CLI
- React Native development environment

### Installation

1. **Clone the repository**
   ```bash
   git clone <your-repo-url>
   cd legal-buddy-app
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up OpenAI API (Optional but Recommended)**
   - Go to [OpenAI Platform](https://platform.openai.com/)
   - Create an account and get an API key
   - Open `config/api.ts`
   - Replace `YOUR_OPENAI_API_KEY_HERE` with your actual API key

4. **Start the development server**
   ```bash
   npx expo start
   ```

5. **Run on your device**
   - Scan the QR code with Expo Go app (mobile)
   - Press `w` for web version
   - Press `a` for Android emulator
   - Press `i` for iOS simulator

## 🔧 Configuration

### API Setup
The app works with both real AI (OpenAI) and enhanced mock responses:

**With OpenAI API:**
- Get accurate, real-time AI responses
- Advanced legal analysis
- Up-to-date legal information

**Without API Key:**
- Enhanced mock responses for Indian law
- Pre-built answers for common questions
- Still fully functional for learning

### Customization
- Modify `quickQuestions` array in `AskAIScreen.tsx` for different preset questions
- Update legal knowledge base in `generateIndianLegalResponse` function
- Customize UI colors and styling in the StyleSheet objects

## 📱 Usage

### Asking Legal Questions
1. **Type your question** in the input field
2. **Use quick questions** for common legal queries
3. **Get detailed responses** with relevant sections and case law
4. **Save important answers** to your journal

### Example Questions
- "What is Section 420 of IPC?"
- "Article 21 of Indian Constitution"
- "Right to Information Act"
- "Domestic Violence Act provisions"
- "Consumer Protection Act 2019"

### Journal Management
- **View saved entries** in the Journal tab
- **Edit or delete** entries as needed
- **Search by category** using the tag system

## 🏗️ Architecture

### Tech Stack
- **React Native** with Expo
- **TypeScript** for type safety
- **React Navigation** for navigation
- **Context API** for state management
- **OpenAI API** for AI responses

### Project Structure
```
legal-buddy-app/
├── screens/           # Main app screens
├── context/          # React Context for state management
├── config/           # API and app configuration
├── types/            # TypeScript type definitions
└── App.tsx           # Main app component
```

## 🎨 Design System

### Colors
- **Primary**: Indigo (#6366F1)
- **Success**: Green (#059669)
- **Warning**: Amber (#F59E0B)
- **Background**: Slate (#F8FAFC)

### Typography
- **Headers**: Bold, large text for titles
- **Body**: Regular text for content
- **Labels**: Medium weight for form labels
- **Captions**: Small text for metadata

## 🔒 Legal Disclaimer

This app provides general legal information for educational purposes only. It does not constitute legal advice. Always consult with a qualified lawyer for specific legal matters.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support, please open an issue on GitHub or contact the development team.

---

**Made with ❤️ for the Indian legal community**
