{"version": 3, "names": ["ensureBlock", "node", "key", "result", "toBlock"], "sources": ["../../src/converters/ensureBlock.ts"], "sourcesContent": ["import toBlock from \"./toBlock\";\nimport type * as t from \"..\";\n\n/**\n * Ensure the `key` (defaults to \"body\") of a `node` is a block.\n * Casting it to a block if it is not.\n *\n * Returns the BlockStatement\n */\nexport default function ensureBlock(\n  node: t.Node,\n  key: string = \"body\",\n): t.BlockStatement {\n  // @ts-expect-error Fixme: key may not exist in node, consider remove key = \"body\"\n  const result = toBlock(node[key], node);\n  // @ts-expect-error Fixme: key may not exist in node, consider remove key = \"body\"\n  node[key] = result;\n  return result;\n}\n"], "mappings": ";;;;;;;AAAA;;AASe,SAASA,WAAT,CACbC,IADa,EAEbC,GAAW,GAAG,MAFD,EAGK;EAElB,MAAMC,MAAM,GAAG,IAAAC,gBAAA,EAAQH,IAAI,CAACC,GAAD,CAAZ,EAAmBD,IAAnB,CAAf;EAEAA,IAAI,CAACC,GAAD,CAAJ,GAAYC,MAAZ;EACA,OAAOA,MAAP;AACD"}