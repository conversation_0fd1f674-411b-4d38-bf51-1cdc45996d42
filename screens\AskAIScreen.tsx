"use client"

import type React from "react"
import { useState, useEffect } from "react"
import { View, Text, TextInput, TouchableOpacity, ScrollView, ActivityIndicator, StyleSheet, Dimensions, Animated } from "react-native"
import { LinearGradient } from "expo-linear-gradient"
import { BlurView } from "expo-blur"
import { Ionicons } from "@expo/vector-icons"
import { useJournal } from "../context/JournalContext"
import { API_CONFIG } from "../config/api"

const { width } = Dimensions.get('window')

type AIResponse = {
  answer: string
  tag: string
}

const AskAIScreen: React.FC = () => {
  const [question, setQuestion] = useState("")
  const [response, setResponse] = useState<AIResponse | null>(null)
  const [loading, setLoading] = useState(false)
  const { addEntry } = useJournal()
  const [animatedValue] = useState(new Animated.Value(0))
  const [pulseAnim] = useState(new Animated.Value(1))
  const [typingAnim] = useState(new Animated.Value(0))

  const quickQuestions = [
    "What is Section 420 of IPC?",
    "Article 21 of Indian Constitution",
    "Right to Information Act",
    "Domestic Violence Act",
    "Consumer Protection Act",
    "Property Rights in India",
    "Fundamental Rights Overview",
    "Criminal Procedure Code"
  ]

  useEffect(() => {
    // Continuous background animation
    const backgroundAnimation = Animated.loop(
      Animated.sequence([
        Animated.timing(animatedValue, {
          toValue: 1,
          duration: 4000,
          useNativeDriver: false,
        }),
        Animated.timing(animatedValue, {
          toValue: 0,
          duration: 4000,
          useNativeDriver: false,
        }),
      ])
    )
    backgroundAnimation.start()

    // Pulse animation for interactive elements
    const pulseAnimation = Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1.05,
          duration: 2000,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 2000,
          useNativeDriver: true,
        }),
      ])
    )
    pulseAnimation.start()
  }, [])

  useEffect(() => {
    // Typing animation when user types
    if (question.length > 0) {
      Animated.timing(typingAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: false,
      }).start()
    } else {
      Animated.timing(typingAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: false,
      }).start()
    }
  }, [question])

  const handleAskAI = async () => {
    if (!question.trim()) return

    setLoading(true)

    try {
      // Only call OpenAI API if a valid API key is configured
      if (API_CONFIG.OPENAI_API_KEY !== 'YOUR_OPENAI_API_KEY_HERE') {
        const response = await fetch(API_CONFIG.OPENAI_ENDPOINT, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${API_CONFIG.OPENAI_API_KEY}`,
          },
          body: JSON.stringify({
            model: API_CONFIG.MODEL,
            messages: [
              {
                role: 'system',
                content: `You are an expert Indian legal assistant with deep knowledge of the Indian Constitution, Indian Penal Code (IPC), Code of Criminal Procedure (CrPC), Code of Civil Procedure (CPC), and other Indian laws. Provide accurate, detailed legal information specific to Indian law. Always mention relevant sections, articles, and case law when applicable. Include disclaimers that this is for informational purposes only and not legal advice.`
              },
              {
                role: 'user',
                content: question
              }
            ],
            max_tokens: API_CONFIG.MAX_TOKENS,
            temperature: API_CONFIG.TEMPERATURE
          })
        });

        if (!response.ok) {
          throw new Error('Failed to get AI response');
        }

        const data = await response.json();
        const aiAnswer = data.choices[0].message.content;

        // Determine tag based on question content
        const tag = determineTag(question);

        const aiResponse: AIResponse = {
          answer: aiAnswer,
          tag: tag,
        }

        setResponse(aiResponse)
        return;
      }

      // Fallback to enhanced mock response for Indian legal questions
      const aiResponse: AIResponse = {
        answer: generateIndianLegalResponse(question),
        tag: determineTag(question),
      }
      setResponse(aiResponse)
    } catch (error) {
      console.error('Error calling AI:', error);
      // Fallback to enhanced mock response for Indian legal questions
      const aiResponse: AIResponse = {
        answer: generateIndianLegalResponse(question),
        tag: determineTag(question),
      }
      setResponse(aiResponse)
    } finally {
      setLoading(false)
    }
  }

  const determineTag = (question: string): string => {
    const lowerQuestion = question.toLowerCase();

    if (lowerQuestion.includes('constitution') || lowerQuestion.includes('article') || lowerQuestion.includes('fundamental right')) {
      return 'Constitutional Law';
    } else if (lowerQuestion.includes('ipc') || lowerQuestion.includes('section 420') || lowerQuestion.includes('criminal') || lowerQuestion.includes('murder') || lowerQuestion.includes('theft')) {
      return 'Criminal Law';
    } else if (lowerQuestion.includes('contract') || lowerQuestion.includes('property') || lowerQuestion.includes('civil')) {
      return 'Civil Law';
    } else if (lowerQuestion.includes('family') || lowerQuestion.includes('marriage') || lowerQuestion.includes('divorce')) {
      return 'Family Law';
    } else if (lowerQuestion.includes('employment') || lowerQuestion.includes('labour') || lowerQuestion.includes('worker')) {
      return 'Labour Law';
    } else if (lowerQuestion.includes('tax') || lowerQuestion.includes('gst') || lowerQuestion.includes('income tax')) {
      return 'Tax Law';
    } else {
      return 'General Law';
    }
  }

  const generateIndianLegalResponse = (question: string): string => {
    const lowerQuestion = question.toLowerCase();

    if (lowerQuestion.includes('section 420')) {
      return `Section 420 of the Indian Penal Code (IPC) deals with "Cheating and dishonestly inducing delivery of property."

**Definition:** Whoever cheats and thereby dishonestly induces the person deceived to deliver any property to any person, or to make, alter or destroy the whole or any part of a valuable security, or anything which is signed or sealed, and which is capable of being converted into a valuable security, shall be punished with imprisonment of either description for a term which may extend to seven years, and shall also be liable to fine.

**Key Elements:**
1. Cheating (as defined in Section 415 IPC)
2. Dishonest inducement
3. Delivery of property or valuable security

**Punishment:** Imprisonment up to 7 years + Fine

**Related Sections:**
- Section 415: Definition of cheating
- Section 417: Punishment for cheating
- Section 419: Punishment for cheating by personation

**Important Case Law:**
- State of Maharashtra v. Dr. Praful B. Desai (2003)
- Hridaya Ranjan Prasad Verma v. State of Bihar (2000)

*Disclaimer: This information is for educational purposes only and does not constitute legal advice. Please consult a qualified lawyer for specific legal matters.*`;
    } else if (lowerQuestion.includes('article') && lowerQuestion.includes('constitution')) {
      return `The Indian Constitution contains 395 Articles (originally) organized into 22 Parts. Here are some key articles:

**Fundamental Rights (Articles 12-35):**
- Article 14: Right to Equality
- Article 19: Freedom of Speech and Expression
- Article 21: Right to Life and Personal Liberty
- Article 25: Freedom of Religion

**Directive Principles (Articles 36-51):**
- Article 39: Equal justice and free legal aid
- Article 44: Uniform civil code
- Article 48: Protection of environment

**Union Government (Articles 52-151):**
- Article 53: Executive power of the Union
- Article 74: Council of Ministers
- Article 76: Attorney General

Please specify which article you'd like detailed information about.

*Disclaimer: This information is for educational purposes only and does not constitute legal advice.*`;
    } else {
      return `Thank you for your question about Indian law. Based on your query, here's some general information:

Indian legal system is based on common law principles with statutory modifications. Key sources include:

**Primary Sources:**
- The Constitution of India (1950)
- Central and State Legislation
- Judicial Precedents (Case Law)

**Major Codes:**
- Indian Penal Code (IPC) - Criminal offenses
- Code of Criminal Procedure (CrPC) - Criminal procedure
- Code of Civil Procedure (CPC) - Civil procedure
- Indian Evidence Act - Rules of evidence

For specific legal advice related to your situation, please consult with a qualified advocate or legal practitioner who can provide personalized guidance based on the facts of your case.

*Disclaimer: This information is for educational purposes only and does not constitute legal advice. Please consult a qualified lawyer for specific legal matters.*`;
    }
  }

  const handleSaveToJournal = () => {
    if (question && response) {
      addEntry({
        id: Date.now().toString(),
        question,
        answer: response.answer,
        tag: response.tag,
        date: new Date().toISOString(),
      })

      // Reset the form
      setQuestion("")
      setResponse(null)
    }
  }

  const backgroundInterpolation = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: ['rgba(99, 102, 241, 0.05)', 'rgba(139, 92, 246, 0.1)']
  })

  const typingBorderColor = typingAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['rgba(99, 102, 241, 0.3)', 'rgba(99, 102, 241, 0.8)']
  })

  return (
    <View style={styles.container}>
      {/* Animated Background */}
      <Animated.View style={[styles.animatedBackground, { backgroundColor: backgroundInterpolation }]} />

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Futuristic Header */}
        <View style={styles.header}>
          <LinearGradient
            colors={['rgba(99, 102, 241, 0.1)', 'rgba(139, 92, 246, 0.05)']}
            style={styles.headerGradient}
          >
            <Animated.View style={[styles.headerContent, { transform: [{ scale: pulseAnim }] }]}>
              <LinearGradient
                colors={['#6366F1', '#8B5CF6', '#EC4899']}
                style={styles.headerIcon}
              >
                <Ionicons name="cpu" size={32} color="#FFFFFF" />
              </LinearGradient>
              <Text style={styles.title}>QUANTUM LEGAL AI</Text>
              <Text style={styles.subtitle}>Neural Network • Indian Law Database</Text>

              <View style={styles.statusContainer}>
                <View style={styles.statusBadge}>
                  <View style={styles.pulsingDot} />
                  <Text style={styles.statusText}>NEURAL ACTIVE</Text>
                </View>
                <View style={styles.versionBadge}>
                  <Text style={styles.versionText}>v3.0.1</Text>
                </View>
              </View>
            </Animated.View>
          </LinearGradient>
        </View>

        {/* Neural Quick Access */}
        <View style={styles.quickQuestionsContainer}>
          <View style={styles.sectionHeader}>
            <Ionicons name="flash" size={16} color="#6366F1" />
            <Text style={styles.quickQuestionsTitle}>NEURAL QUICK ACCESS</Text>
          </View>
          <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.quickQuestionsScroll}>
            {quickQuestions.map((q, index) => (
              <TouchableOpacity
                key={index}
                style={styles.quickQuestionButton}
                onPress={() => setQuestion(q)}
                activeOpacity={0.8}
              >
                <BlurView intensity={20} style={styles.quickQuestionBlur}>
                  <LinearGradient
                    colors={['rgba(99, 102, 241, 0.1)', 'rgba(139, 92, 246, 0.1)']}
                    style={styles.quickQuestionGradient}
                  >
                    <Text style={styles.quickQuestionText}>{q}</Text>
                    <Ionicons name="arrow-forward" size={12} color="#6366F1" />
                  </LinearGradient>
                </BlurView>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>

        {/* Neural Input Interface */}
        <View style={styles.inputCard}>
          <BlurView intensity={30} style={styles.inputBlur}>
            <LinearGradient
              colors={['rgba(15, 23, 42, 0.8)', 'rgba(30, 41, 59, 0.6)']}
              style={styles.inputGradient}
            >
              <View style={styles.inputHeader}>
                <View style={styles.inputHeaderLeft}>
                  <LinearGradient
                    colors={['#6366F1', '#8B5CF6']}
                    style={styles.inputIcon}
                  >
                    <Ionicons name="terminal" size={18} color="#FFFFFF" />
                  </LinearGradient>
                  <Text style={styles.inputLabel}>NEURAL QUERY INTERFACE</Text>
                </View>
                <View style={styles.inputBadge}>
                  <Text style={styles.inputBadgeText}>QUANTUM</Text>
                </View>
              </View>

              <Animated.View style={[styles.textInputContainer, { borderColor: typingBorderColor }]}>
                <TextInput
                  style={styles.textInput}
                  placeholder="Initialize legal query... (Indian Constitution, IPC, CrPC, Legal Rights)"
                  placeholderTextColor="#64748B"
                  multiline
                  textAlignVertical="top"
                  value={question}
                  onChangeText={setQuestion}
                />
                {question.length > 0 && (
                  <Animated.View style={[styles.typingIndicator, { opacity: typingAnim }]}>
                    <View style={styles.typingDots}>
                      <View style={styles.typingDot} />
                      <View style={styles.typingDot} />
                      <View style={styles.typingDot} />
                    </View>
                  </Animated.View>
                )}
              </Animated.View>

              <View style={styles.inputFooter}>
                <View style={styles.inputStats}>
                  <Text style={styles.characterCount}>{question.length}/1000 chars</Text>
                  <View style={styles.inputTip}>
                    <Ionicons name="information-circle" size={12} color="#6366F1" />
                    <Text style={styles.tipText}>Neural processing active</Text>
                  </View>
                </View>
              </View>
            </LinearGradient>
          </BlurView>
        </View>

        {/* Neural Processing Button */}
        <Animated.View style={[styles.buttonContainer, { transform: [{ scale: pulseAnim }] }]}>
          <TouchableOpacity
            style={styles.askButton}
            onPress={handleAskAI}
            disabled={!question.trim() || loading}
            activeOpacity={0.9}
          >
            <LinearGradient
              colors={question.trim()
                ? ['#6366F1', '#8B5CF6', '#EC4899']
                : ['rgba(99, 102, 241, 0.3)', 'rgba(139, 92, 246, 0.3)']}
              style={styles.buttonGradient}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 0 }}
            >
              {loading ? (
                <View style={styles.loadingContainer}>
                  <ActivityIndicator color="#FFFFFF" size="small" />
                  <Text style={styles.loadingText}>NEURAL PROCESSING...</Text>
                  <View style={styles.loadingDots}>
                    <View style={styles.loadingDot} />
                    <View style={styles.loadingDot} />
                    <View style={styles.loadingDot} />
                  </View>
                </View>
              ) : (
                <View style={styles.buttonContent}>
                  <Ionicons name="rocket" size={20} color="#FFFFFF" />
                  <Text style={styles.askButtonText}>INITIATE NEURAL QUERY</Text>
                  <Ionicons name="chevron-forward" size={16} color="#FFFFFF" />
                </View>
              )}
            </LinearGradient>
          </TouchableOpacity>
        </Animated.View>

        {/* Neural Response Interface */}
        {response && (
          <Animated.View style={[styles.responseCard, { opacity: animatedValue.interpolate({ inputRange: [0, 1], outputRange: [0.9, 1] }) }]}>
            <BlurView intensity={40} style={styles.responseBlur}>
              <LinearGradient
                colors={['rgba(15, 23, 42, 0.9)', 'rgba(30, 41, 59, 0.8)']}
                style={styles.responseGradient}
              >
                <View style={styles.responseHeader}>
                  <View style={styles.responseHeaderLeft}>
                    <LinearGradient
                      colors={['#6366F1', '#8B5CF6']}
                      style={styles.tagContainer}
                    >
                      <Ionicons name="shield-checkmark" size={12} color="#FFFFFF" />
                      <Text style={styles.tagText}>{response.tag}</Text>
                    </LinearGradient>
                    <View style={styles.aiIndicator}>
                      <View style={styles.aiPulse} />
                      <Text style={styles.aiText}>NEURAL RESPONSE</Text>
                    </View>
                  </View>
                  <View style={styles.responseActions}>
                    <TouchableOpacity style={styles.actionIcon}>
                      <Ionicons name="copy-outline" size={16} color="#64748B" />
                    </TouchableOpacity>
                    <TouchableOpacity style={styles.actionIcon}>
                      <Ionicons name="share-outline" size={16} color="#64748B" />
                    </TouchableOpacity>
                  </View>
                </View>

                <View style={styles.responseContent}>
                  <Text style={styles.responseText}>{response.answer}</Text>
                </View>

                <View style={styles.responseFooter}>
                  <TouchableOpacity
                    style={styles.saveButton}
                    onPress={handleSaveToJournal}
                    activeOpacity={0.8}
                  >
                    <LinearGradient
                      colors={['rgba(99, 102, 241, 0.2)', 'rgba(139, 92, 246, 0.2)']}
                      style={styles.saveButtonGradient}
                    >
                      <Ionicons name="download" size={18} color="#6366F1" />
                      <Text style={styles.saveButtonText}>SAVE TO NEURAL BANK</Text>
                    </LinearGradient>
                  </TouchableOpacity>
                </View>
              </LinearGradient>
            </BlurView>
          </Animated.View>
        )}
      </ScrollView>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'transparent',
  },
  animatedBackground: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: 16,
  },
  header: {
    marginBottom: 20,
    borderRadius: 20,
    overflow: 'hidden',
  },
  headerGradient: {
    padding: 24,
  },
  headerContent: {
    alignItems: 'center',
  },
  headerIcon: {
    width: 80,
    height: 80,
    borderRadius: 40,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
    shadowColor: '#6366F1',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.4,
    shadowRadius: 16,
    elevation: 12,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#FFFFFF',
    textAlign: 'center',
    marginBottom: 8,
    letterSpacing: 2,
  },
  subtitle: {
    fontSize: 14,
    color: '#94A3B8',
    textAlign: 'center',
    marginBottom: 16,
    letterSpacing: 1,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(16, 185, 129, 0.2)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: '#10B981',
  },
  pulsingDot: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: '#10B981',
    marginRight: 6,
  },
  statusText: {
    fontSize: 10,
    fontWeight: 'bold',
    color: '#10B981',
    letterSpacing: 1,
  },
  versionBadge: {
    backgroundColor: 'rgba(99, 102, 241, 0.2)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#6366F1',
  },
  versionText: {
    fontSize: 10,
    fontWeight: 'bold',
    color: '#6366F1',
  },
  quickQuestionsContainer: {
    marginBottom: 24,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    paddingHorizontal: 4,
  },
  quickQuestionsTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginLeft: 8,
    letterSpacing: 1,
  },
  quickQuestionsScroll: {
    paddingLeft: 4,
  },
  quickQuestionButton: {
    marginRight: 12,
    borderRadius: 16,
    overflow: 'hidden',
  },
  quickQuestionBlur: {
    borderRadius: 16,
    overflow: 'hidden',
  },
  quickQuestionGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    minWidth: 160,
    borderWidth: 1,
    borderColor: 'rgba(99, 102, 241, 0.3)',
  },
  quickQuestionText: {
    fontSize: 12,
    color: '#E2E8F0',
    fontWeight: '500',
    flex: 1,
  },
  inputCard: {
    marginBottom: 24,
    borderRadius: 20,
    overflow: 'hidden',
  },
  inputBlur: {
    borderRadius: 20,
    overflow: 'hidden',
  },
  inputGradient: {
    padding: 20,
    borderWidth: 1,
    borderColor: 'rgba(99, 102, 241, 0.3)',
  },
  inputHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  inputHeaderLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  inputIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#FFFFFF',
    letterSpacing: 1,
  },
  inputBadge: {
    backgroundColor: 'rgba(236, 72, 153, 0.2)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#EC4899',
  },
  inputBadgeText: {
    fontSize: 9,
    fontWeight: 'bold',
    color: '#EC4899',
    letterSpacing: 1,
  },
  textInputContainer: {
    borderRadius: 16,
    borderWidth: 2,
    overflow: 'hidden',
    position: 'relative',
  },
  textInput: {
    minHeight: 120,
    fontSize: 16,
    color: '#FFFFFF',
    lineHeight: 24,
    textAlignVertical: 'top',
    padding: 16,
    backgroundColor: 'rgba(15, 23, 42, 0.5)',
  },
  typingIndicator: {
    position: 'absolute',
    bottom: 8,
    right: 12,
  },
  typingDots: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  typingDot: {
    width: 4,
    height: 4,
    borderRadius: 2,
    backgroundColor: '#6366F1',
    marginHorizontal: 2,
  },
  inputFooter: {
    marginTop: 12,
  },
  inputStats: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  characterCount: {
    fontSize: 11,
    color: '#64748B',
    fontWeight: '500',
  },
  inputTip: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  tipText: {
    fontSize: 10,
    color: '#6366F1',
    marginLeft: 4,
    fontWeight: '500',
  },
  buttonContainer: {
    marginBottom: 24,
  },
  askButton: {
    borderRadius: 16,
    overflow: 'hidden',
    shadowColor: '#6366F1',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.4,
    shadowRadius: 16,
    elevation: 12,
  },
  buttonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 18,
    paddingHorizontal: 24,
  },
  buttonContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  askButtonText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginHorizontal: 12,
    letterSpacing: 1,
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginLeft: 8,
    letterSpacing: 1,
  },
  loadingDots: {
    flexDirection: 'row',
    marginLeft: 8,
  },
  loadingDot: {
    width: 4,
    height: 4,
    borderRadius: 2,
    backgroundColor: '#FFFFFF',
    marginHorizontal: 2,
  },
  responseCard: {
    marginBottom: 24,
    borderRadius: 20,
    overflow: 'hidden',
  },
  responseBlur: {
    borderRadius: 20,
    overflow: 'hidden',
  },
  responseGradient: {
    padding: 20,
    borderWidth: 1,
    borderColor: 'rgba(99, 102, 241, 0.3)',
  },
  responseHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  responseHeaderLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  tagContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
    marginRight: 12,
  },
  tagText: {
    fontSize: 10,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginLeft: 4,
    letterSpacing: 1,
  },
  aiIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(16, 185, 129, 0.2)',
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: '#10B981',
  },
  aiPulse: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: '#10B981',
    marginRight: 6,
  },
  aiText: {
    fontSize: 9,
    fontWeight: 'bold',
    color: '#10B981',
    letterSpacing: 1,
  },
  responseActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  actionIcon: {
    padding: 8,
    marginLeft: 4,
  },
  responseContent: {
    marginBottom: 20,
  },
  responseText: {
    fontSize: 15,
    color: '#E2E8F0',
    lineHeight: 24,
  },
  responseFooter: {
    alignItems: 'center',
  },
  saveButton: {
    borderRadius: 12,
    overflow: 'hidden',
    width: '100%',
  },
  saveButtonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 14,
    paddingHorizontal: 20,
    borderWidth: 1,
    borderColor: 'rgba(99, 102, 241, 0.3)',
  },
  saveButtonText: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#6366F1',
    marginLeft: 8,
    letterSpacing: 1,
  },
})

export default AskAIScreen
