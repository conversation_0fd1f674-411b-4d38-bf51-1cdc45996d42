"use client"

import type React from "react"
import { useState } from "react"
import { View, Text, TextInput, TouchableOpacity, ScrollView, ActivityIndicator, StyleSheet, Dimensions } from "react-native"
import { SafeAreaView } from "react-native-safe-area-context"
import { Ionicons } from "@expo/vector-icons"
import { useJournal } from "../context/JournalContext"

const { width } = Dimensions.get('window')

type AIResponse = {
  answer: string
  tag: string
}

const AskAIScreen: React.FC = () => {
  const [question, setQuestion] = useState("")
  const [response, setResponse] = useState<AIResponse | null>(null)
  const [loading, setLoading] = useState(false)
  const { addEntry } = useJournal()

  const handleAskAI = () => {
    if (!question.trim()) return

    setLoading(true)

    // Simulate API call with timeout
    setTimeout(() => {
      // Mock AI response
      const mockResponse: AIResponse = {
        answer: `Based on your query about "${question.substring(0, 30)}${question.length > 30 ? "..." : ""}", here's a general answer: This is a simulated AI response that would provide legal information related to your question. Please note that this is not actual legal advice and you should consult with a qualified attorney for specific legal matters.`,
        tag: getRandomTag(),
      }

      setResponse(mockResponse)
      setLoading(false)
    }, 1500)
  }

  const getRandomTag = (): string => {
    const tags = ["Housing", "Privacy", "Employment", "Family", "Consumer", "Criminal"]
    return tags[Math.floor(Math.random() * tags.length)]
  }

  const handleSaveToJournal = () => {
    if (question && response) {
      addEntry({
        id: Date.now().toString(),
        question,
        answer: response.answer,
        tag: response.tag,
        date: new Date().toISOString(),
      })

      // Reset the form
      setQuestion("")
      setResponse(null)
    }
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Header Section */}
        <View style={styles.header}>
          <View style={styles.headerIcon}>
            <Ionicons name="chatbubble-ellipses" size={28} color="#6366F1" />
          </View>
          <Text style={styles.title}>Ask a Legal Question</Text>
          <Text style={styles.subtitle}>Get AI-powered legal information instantly</Text>
        </View>

        {/* Question Input Card */}
        <View style={styles.inputCard}>
          <View style={styles.inputHeader}>
            <Ionicons name="create-outline" size={20} color="#6366F1" />
            <Text style={styles.inputLabel}>Your Question</Text>
          </View>
          <TextInput
            style={styles.textInput}
            placeholder="Type your legal question here..."
            placeholderTextColor="#9CA3AF"
            multiline
            textAlignVertical="top"
            value={question}
            onChangeText={setQuestion}
          />
          <View style={styles.inputFooter}>
            <Text style={styles.characterCount}>{question.length} characters</Text>
          </View>
        </View>

        {/* Ask AI Button */}
        <TouchableOpacity
          style={[
            styles.askButton,
            { backgroundColor: question.trim() ? "#6366F1" : "#C7D2FE" }
          ]}
          onPress={handleAskAI}
          disabled={!question.trim() || loading}
          activeOpacity={0.8}
        >
          {loading ? (
            <ActivityIndicator color="#FFFFFF" size="small" />
          ) : (
            <>
              <Ionicons name="send" size={20} color="#FFFFFF" />
              <Text style={styles.askButtonText}>Ask AI</Text>
            </>
          )}
        </TouchableOpacity>

        {/* AI Response Card */}
        {response && (
          <View style={styles.responseCard}>
            <View style={styles.responseHeader}>
              <View style={styles.tagContainer}>
                <Ionicons name="pricetag" size={14} color="#6366F1" />
                <Text style={styles.tagText}>{response.tag}</Text>
              </View>
              <View style={styles.aiIndicator}>
                <Ionicons name="sparkles" size={16} color="#F59E0B" />
                <Text style={styles.aiText}>AI Response</Text>
              </View>
            </View>

            <Text style={styles.responseText}>{response.answer}</Text>

            <TouchableOpacity
              style={styles.saveButton}
              onPress={handleSaveToJournal}
              activeOpacity={0.7}
            >
              <Ionicons name="bookmark-outline" size={20} color="#6366F1" />
              <Text style={styles.saveButtonText}>Save to Journal</Text>
            </TouchableOpacity>
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8FAFC',
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: 20,
  },
  header: {
    alignItems: 'center',
    paddingVertical: 30,
    paddingBottom: 40,
  },
  headerIcon: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#EEF2FF',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#1E293B',
    textAlign: 'center',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#64748B',
    textAlign: 'center',
    lineHeight: 24,
  },
  inputCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 20,
    marginBottom: 24,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  inputHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#374151',
    marginLeft: 8,
  },
  textInput: {
    minHeight: 120,
    fontSize: 16,
    color: '#374151',
    lineHeight: 24,
    textAlignVertical: 'top',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 12,
    padding: 16,
    backgroundColor: '#F9FAFB',
  },
  inputFooter: {
    alignItems: 'flex-end',
    marginTop: 8,
  },
  characterCount: {
    fontSize: 12,
    color: '#9CA3AF',
  },
  askButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    marginBottom: 24,
    shadowColor: '#6366F1',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
  },
  askButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
    marginLeft: 8,
  },
  responseCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 20,
    marginBottom: 24,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
    borderLeftWidth: 4,
    borderLeftColor: '#6366F1',
  },
  responseHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  tagContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#EEF2FF',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
  },
  tagText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#6366F1',
    marginLeft: 4,
  },
  aiIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FEF3C7',
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 16,
  },
  aiText: {
    fontSize: 11,
    fontWeight: '500',
    color: '#D97706',
    marginLeft: 4,
  },
  responseText: {
    fontSize: 16,
    color: '#374151',
    lineHeight: 24,
    marginBottom: 20,
  },
  saveButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#F8FAFC',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 10,
  },
  saveButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#6366F1',
    marginLeft: 8,
  },
})

export default AskAIScreen
