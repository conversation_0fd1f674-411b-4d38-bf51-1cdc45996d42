"use client"

import type React from "react"
import { useState } from "react"
import { View, Text, TextInput, TouchableOpacity, ScrollView, ActivityIndicator, StyleSheet, Dimensions } from "react-native"
import { SafeAreaView } from "react-native-safe-area-context"
import { Ionicons } from "@expo/vector-icons"
import { useJournal } from "../context/JournalContext"
import { API_CONFIG } from "../config/api"

const { width } = Dimensions.get('window')

type AIResponse = {
  answer: string
  tag: string
}

const AskAIScreen: React.FC = () => {
  const [question, setQuestion] = useState("")
  const [response, setResponse] = useState<AIResponse | null>(null)
  const [loading, setLoading] = useState(false)
  const { addEntry } = useJournal()

  const quickQuestions = [
    "What is Section 420 of IPC?",
    "Article 21 of Indian Constitution",
    "Right to Information Act",
    "Domestic Violence Act",
    "Consumer Protection Act",
    "Property Rights in India"
  ]

  const handleAskAI = async () => {
    if (!question.trim()) return

    setLoading(true)

    try {
      // Only call OpenAI API if a valid API key is configured
      if (API_CONFIG.OPENAI_API_KEY !== 'YOUR_OPENAI_API_KEY_HERE') {
        const response = await fetch(API_CONFIG.OPENAI_ENDPOINT, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${API_CONFIG.OPENAI_API_KEY}`,
          },
          body: JSON.stringify({
            model: API_CONFIG.MODEL,
            messages: [
              {
                role: 'system',
                content: `You are an expert Indian legal assistant with deep knowledge of the Indian Constitution, Indian Penal Code (IPC), Code of Criminal Procedure (CrPC), Code of Civil Procedure (CPC), and other Indian laws. Provide accurate, detailed legal information specific to Indian law. Always mention relevant sections, articles, and case law when applicable. Include disclaimers that this is for informational purposes only and not legal advice.`
              },
              {
                role: 'user',
                content: question
              }
            ],
            max_tokens: API_CONFIG.MAX_TOKENS,
            temperature: API_CONFIG.TEMPERATURE
          })
        });

        if (!response.ok) {
          throw new Error('Failed to get AI response');
        }

        const data = await response.json();
        const aiAnswer = data.choices[0].message.content;

        // Determine tag based on question content
        const tag = determineTag(question);

        const aiResponse: AIResponse = {
          answer: aiAnswer,
          tag: tag,
        }

        setResponse(aiResponse)
        return;
      }

      // Fallback to enhanced mock response for Indian legal questions
      const aiResponse: AIResponse = {
        answer: generateIndianLegalResponse(question),
        tag: determineTag(question),
      }
      setResponse(aiResponse)
    } catch (error) {
      console.error('Error calling AI:', error);
      // Fallback to enhanced mock response for Indian legal questions
      const aiResponse: AIResponse = {
        answer: generateIndianLegalResponse(question),
        tag: determineTag(question),
      }
      setResponse(aiResponse)
    } finally {
      setLoading(false)
    }
  }

  const determineTag = (question: string): string => {
    const lowerQuestion = question.toLowerCase();

    if (lowerQuestion.includes('constitution') || lowerQuestion.includes('article') || lowerQuestion.includes('fundamental right')) {
      return 'Constitutional Law';
    } else if (lowerQuestion.includes('ipc') || lowerQuestion.includes('section 420') || lowerQuestion.includes('criminal') || lowerQuestion.includes('murder') || lowerQuestion.includes('theft')) {
      return 'Criminal Law';
    } else if (lowerQuestion.includes('contract') || lowerQuestion.includes('property') || lowerQuestion.includes('civil')) {
      return 'Civil Law';
    } else if (lowerQuestion.includes('family') || lowerQuestion.includes('marriage') || lowerQuestion.includes('divorce')) {
      return 'Family Law';
    } else if (lowerQuestion.includes('employment') || lowerQuestion.includes('labour') || lowerQuestion.includes('worker')) {
      return 'Labour Law';
    } else if (lowerQuestion.includes('tax') || lowerQuestion.includes('gst') || lowerQuestion.includes('income tax')) {
      return 'Tax Law';
    } else {
      return 'General Law';
    }
  }

  const generateIndianLegalResponse = (question: string): string => {
    const lowerQuestion = question.toLowerCase();

    if (lowerQuestion.includes('section 420')) {
      return `Section 420 of the Indian Penal Code (IPC) deals with "Cheating and dishonestly inducing delivery of property."

**Definition:** Whoever cheats and thereby dishonestly induces the person deceived to deliver any property to any person, or to make, alter or destroy the whole or any part of a valuable security, or anything which is signed or sealed, and which is capable of being converted into a valuable security, shall be punished with imprisonment of either description for a term which may extend to seven years, and shall also be liable to fine.

**Key Elements:**
1. Cheating (as defined in Section 415 IPC)
2. Dishonest inducement
3. Delivery of property or valuable security

**Punishment:** Imprisonment up to 7 years + Fine

**Related Sections:**
- Section 415: Definition of cheating
- Section 417: Punishment for cheating
- Section 419: Punishment for cheating by personation

**Important Case Law:**
- State of Maharashtra v. Dr. Praful B. Desai (2003)
- Hridaya Ranjan Prasad Verma v. State of Bihar (2000)

*Disclaimer: This information is for educational purposes only and does not constitute legal advice. Please consult a qualified lawyer for specific legal matters.*`;
    } else if (lowerQuestion.includes('article') && lowerQuestion.includes('constitution')) {
      return `The Indian Constitution contains 395 Articles (originally) organized into 22 Parts. Here are some key articles:

**Fundamental Rights (Articles 12-35):**
- Article 14: Right to Equality
- Article 19: Freedom of Speech and Expression
- Article 21: Right to Life and Personal Liberty
- Article 25: Freedom of Religion

**Directive Principles (Articles 36-51):**
- Article 39: Equal justice and free legal aid
- Article 44: Uniform civil code
- Article 48: Protection of environment

**Union Government (Articles 52-151):**
- Article 53: Executive power of the Union
- Article 74: Council of Ministers
- Article 76: Attorney General

Please specify which article you'd like detailed information about.

*Disclaimer: This information is for educational purposes only and does not constitute legal advice.*`;
    } else {
      return `Thank you for your question about Indian law. Based on your query, here's some general information:

Indian legal system is based on common law principles with statutory modifications. Key sources include:

**Primary Sources:**
- The Constitution of India (1950)
- Central and State Legislation
- Judicial Precedents (Case Law)

**Major Codes:**
- Indian Penal Code (IPC) - Criminal offenses
- Code of Criminal Procedure (CrPC) - Criminal procedure
- Code of Civil Procedure (CPC) - Civil procedure
- Indian Evidence Act - Rules of evidence

For specific legal advice related to your situation, please consult with a qualified advocate or legal practitioner who can provide personalized guidance based on the facts of your case.

*Disclaimer: This information is for educational purposes only and does not constitute legal advice. Please consult a qualified lawyer for specific legal matters.*`;
    }
  }

  const handleSaveToJournal = () => {
    if (question && response) {
      addEntry({
        id: Date.now().toString(),
        question,
        answer: response.answer,
        tag: response.tag,
        date: new Date().toISOString(),
      })

      // Reset the form
      setQuestion("")
      setResponse(null)
    }
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Header Section */}
        <View style={styles.header}>
          <View style={styles.headerIcon}>
            <Ionicons name="library" size={28} color="#6366F1" />
          </View>
          <Text style={styles.title}>Legal AI Assistant</Text>
          <Text style={styles.subtitle}>Expert guidance on Indian Law & Constitution</Text>
          <View style={styles.badgeContainer}>
            <View style={styles.badge}>
              <Ionicons name="shield-checkmark" size={14} color="#059669" />
              <Text style={styles.badgeText}>Indian Law Expert</Text>
            </View>
          </View>
        </View>

        {/* Quick Questions */}
        <View style={styles.quickQuestionsContainer}>
          <Text style={styles.quickQuestionsTitle}>Popular Questions</Text>
          <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.quickQuestionsScroll}>
            {quickQuestions.map((q, index) => (
              <TouchableOpacity
                key={index}
                style={styles.quickQuestionButton}
                onPress={() => setQuestion(q)}
                activeOpacity={0.7}
              >
                <Text style={styles.quickQuestionText}>{q}</Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>

        {/* Question Input Card */}
        <View style={styles.inputCard}>
          <View style={styles.inputHeader}>
            <Ionicons name="document-text" size={20} color="#6366F1" />
            <Text style={styles.inputLabel}>Your Legal Question</Text>
            <View style={styles.inputBadge}>
              <Text style={styles.inputBadgeText}>AI Powered</Text>
            </View>
          </View>
          <TextInput
            style={styles.textInput}
            placeholder="Ask about Indian Constitution, IPC sections, legal procedures, rights, etc..."
            placeholderTextColor="#9CA3AF"
            multiline
            textAlignVertical="top"
            value={question}
            onChangeText={setQuestion}
          />
          <View style={styles.inputFooter}>
            <View style={styles.inputStats}>
              <Text style={styles.characterCount}>{question.length} characters</Text>
              <View style={styles.inputTip}>
                <Ionicons name="bulb" size={12} color="#F59E0B" />
                <Text style={styles.tipText}>Be specific for better results</Text>
              </View>
            </View>
          </View>
        </View>

        {/* Ask AI Button */}
        <TouchableOpacity
          style={[
            styles.askButton,
            { backgroundColor: question.trim() ? "#6366F1" : "#C7D2FE" }
          ]}
          onPress={handleAskAI}
          disabled={!question.trim() || loading}
          activeOpacity={0.8}
        >
          {loading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator color="#FFFFFF" size="small" />
              <Text style={styles.loadingText}>Analyzing your question...</Text>
            </View>
          ) : (
            <>
              <Ionicons name="sparkles" size={20} color="#FFFFFF" />
              <Text style={styles.askButtonText}>Get Legal Guidance</Text>
              <Ionicons name="arrow-forward" size={16} color="#FFFFFF" />
            </>
          )}
        </TouchableOpacity>

        {/* AI Response Card */}
        {response && (
          <View style={styles.responseCard}>
            <View style={styles.responseHeader}>
              <View style={styles.tagContainer}>
                <Ionicons name="pricetag" size={14} color="#6366F1" />
                <Text style={styles.tagText}>{response.tag}</Text>
              </View>
              <View style={styles.aiIndicator}>
                <Ionicons name="sparkles" size={16} color="#F59E0B" />
                <Text style={styles.aiText}>AI Response</Text>
              </View>
            </View>

            <Text style={styles.responseText}>{response.answer}</Text>

            <TouchableOpacity
              style={styles.saveButton}
              onPress={handleSaveToJournal}
              activeOpacity={0.7}
            >
              <Ionicons name="bookmark-outline" size={20} color="#6366F1" />
              <Text style={styles.saveButtonText}>Save to Journal</Text>
            </TouchableOpacity>
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8FAFC',
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: 20,
  },
  header: {
    alignItems: 'center',
    paddingVertical: 30,
    paddingBottom: 20,
  },
  headerIcon: {
    width: 70,
    height: 70,
    borderRadius: 35,
    backgroundColor: '#EEF2FF',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
    shadowColor: '#6366F1',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 6,
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#1E293B',
    textAlign: 'center',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#64748B',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 16,
  },
  badgeContainer: {
    marginTop: 8,
  },
  badge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#ECFDF5',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: '#A7F3D0',
  },
  badgeText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#059669',
    marginLeft: 4,
  },
  quickQuestionsContainer: {
    marginBottom: 24,
  },
  quickQuestionsTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 12,
    paddingHorizontal: 4,
  },
  quickQuestionsScroll: {
    paddingLeft: 4,
  },
  quickQuestionButton: {
    backgroundColor: '#F8FAFC',
    borderWidth: 1,
    borderColor: '#E2E8F0',
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 20,
    marginRight: 12,
    minWidth: 140,
  },
  quickQuestionText: {
    fontSize: 13,
    color: '#475569',
    fontWeight: '500',
    textAlign: 'center',
  },
  inputCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 20,
    marginBottom: 24,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  inputHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#374151',
    marginLeft: 8,
    flex: 1,
  },
  inputBadge: {
    backgroundColor: '#FEF3C7',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  inputBadgeText: {
    fontSize: 10,
    fontWeight: '600',
    color: '#D97706',
  },
  textInput: {
    minHeight: 120,
    fontSize: 16,
    color: '#374151',
    lineHeight: 24,
    textAlignVertical: 'top',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 12,
    padding: 16,
    backgroundColor: '#F9FAFB',
  },
  inputFooter: {
    marginTop: 12,
  },
  inputStats: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  characterCount: {
    fontSize: 12,
    color: '#9CA3AF',
  },
  inputTip: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  tipText: {
    fontSize: 11,
    color: '#F59E0B',
    marginLeft: 4,
    fontWeight: '500',
  },
  askButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    marginBottom: 24,
    shadowColor: '#6366F1',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
  },
  askButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
    marginHorizontal: 8,
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#FFFFFF',
    marginLeft: 8,
  },
  responseCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 20,
    marginBottom: 24,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
    borderLeftWidth: 4,
    borderLeftColor: '#6366F1',
  },
  responseHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  tagContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#EEF2FF',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
  },
  tagText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#6366F1',
    marginLeft: 4,
  },
  aiIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FEF3C7',
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 16,
  },
  aiText: {
    fontSize: 11,
    fontWeight: '500',
    color: '#D97706',
    marginLeft: 4,
  },
  responseText: {
    fontSize: 16,
    color: '#374151',
    lineHeight: 24,
    marginBottom: 20,
  },
  saveButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#F8FAFC',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 10,
  },
  saveButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#6366F1',
    marginLeft: 8,
  },
})

export default AskAIScreen
