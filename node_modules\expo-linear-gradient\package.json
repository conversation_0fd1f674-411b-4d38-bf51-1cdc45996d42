{"name": "expo-linear-gradient", "version": "14.1.4", "description": "Provides a React component that renders a gradient view.", "main": "build/LinearGradient.js", "types": "build/LinearGradient.d.ts", "sideEffects": false, "scripts": {"build": "expo-module build", "clean": "expo-module clean", "lint": "expo-module lint", "test": "expo-module test", "test:rsc": "jest --config jest-rsc.config.js", "prepare": "expo-module prepare", "prepublishOnly": "expo-module prepublishOnly", "expo-module": "expo-module"}, "jest": {"preset": "expo-module-scripts"}, "keywords": ["react-native", "expo", "gradient"], "repository": {"type": "git", "url": "https://github.com/expo/expo.git", "directory": "packages/expo-linear-gradient"}, "bugs": {"url": "https://github.com/expo/expo/issues"}, "author": "650 Industries, Inc.", "license": "MIT", "homepage": "https://docs.expo.dev/versions/latest/sdk/linear-gradient/", "devDependencies": {"@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^15.0.7", "@testing-library/react-native": "^13.1.0", "expo-module-scripts": "^4.1.6"}, "peerDependencies": {"expo": "*", "react": "*", "react-native": "*"}, "gitHead": "84355076bc31a356aa3d23257f387f221885f53d"}