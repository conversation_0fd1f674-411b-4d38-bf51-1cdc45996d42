{"version": "2.0.11", "name": "nativewind", "description": "Use Tailwindcss in your cross-platform React Native applications", "main": "dist/index.js", "types": "dist/index.d.ts", "sideEffects": false, "keywords": ["react-native", "react", "native", "tailwind", "tailwindcss", "theme", "style"], "license": "MIT", "repository": {"type": "git", "url": "https://github.com/marklawlor/nativewind.git"}, "author": {"name": "<PERSON>", "url": "https://github.com/marklawlor"}, "homepage": "https://nativewind.dev", "bugs": {"url": "https://github.com/marklawlor/nativewind/issues"}, "engines": {"node": ">=14.18"}, "scripts": {"test": "jest", "test:debug": "node --inspect-brk node_modules/.bin/jest --runInBand", "lint": "eslint .", "prepublishOnly": "npm run build", "build": "rm -rf dist && tsc"}, "files": ["dist/", "tailwind/", "postcss.js", "babel.js", "plugin.js", "types.d.ts"], "dependencies": {"@babel/generator": "^7.18.7", "@babel/helper-module-imports": "7.18.6", "@babel/types": "7.19.0", "css-mediaquery": "^0.1.2", "css-to-react-native": "^3.0.0", "micromatch": "^4.0.5", "postcss": "^8.4.12", "postcss-calc": "^8.2.4", "postcss-color-functional-notation": "^4.2.2", "postcss-css-variables": "^0.18.0", "postcss-nested": "^5.0.6", "react-is": "^18.1.0", "use-sync-external-store": "^1.1.0"}, "devDependencies": {"@babel/core": "7.19.1", "@babel/plugin-syntax-jsx": "7.18.6", "@cspell/eslint-plugin": "6.10.1", "@testing-library/react-hooks": "8.0.1", "@testing-library/react-native": "11.2.0", "@types/css": "0.0.33", "@types/css-mediaquery": "0.1.1", "@types/css-to-react-native": "3.0.0", "@types/jest": "29.0.3", "@types/micromatch": "4.0.2", "@types/react": "18.0.21", "@types/react-is": "17.0.3", "@types/react-native": "0.70.3", "@types/use-sync-external-store": "0.0.3", "@typescript-eslint/eslint-plugin": "5.38.1", "@typescript-eslint/parser": "5.38.1", "babel-plugin-tester": "10.1.0", "jest": "29.0.3", "jest-environment-jsdom": "29.0.3", "metro-react-native-babel-preset": "0.72.3", "moti": "0.19.0", "react": "18.2.0", "react-dom": "18.2.0", "react-native": "0.70.1", "react-native-svg": "13.2.0", "react-native-web": "0.18.9", "react-test-renderer": "18.2.0", "tailwindcss": "3.1.8", "ts-jest": "29.0.2", "typescript": "4.8.3"}, "peerDependencies": {"tailwindcss": "~3"}}