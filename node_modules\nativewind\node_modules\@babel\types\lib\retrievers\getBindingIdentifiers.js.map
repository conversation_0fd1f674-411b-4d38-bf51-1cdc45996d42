{"version": 3, "names": ["getBindingIdentifiers", "node", "duplicates", "outerOnly", "search", "concat", "ids", "Object", "create", "length", "id", "shift", "keys", "type", "isIdentifier", "_ids", "name", "push", "isExportDeclaration", "isExportAllDeclaration", "isDeclaration", "declaration", "isFunctionDeclaration", "isFunctionExpression", "i", "key", "nodes", "Array", "isArray", "DeclareClass", "DeclareFunction", "DeclareModule", "DeclareVariable", "DeclareInterface", "DeclareTypeAlias", "DeclareOpaqueType", "InterfaceDeclaration", "TypeAlias", "OpaqueType", "CatchClause", "LabeledStatement", "UnaryExpression", "AssignmentExpression", "ImportSpecifier", "ImportNamespaceSpecifier", "ImportDefaultSpecifier", "ImportDeclaration", "ExportSpecifier", "ExportNamespaceSpecifier", "ExportDefaultSpecifier", "FunctionDeclaration", "FunctionExpression", "ArrowFunctionExpression", "ObjectMethod", "ClassMethod", "ClassPrivateMethod", "ForInStatement", "ForOfStatement", "ClassDeclaration", "ClassExpression", "RestElement", "UpdateExpression", "ObjectProperty", "AssignmentPattern", "ArrayPattern", "ObjectPattern", "VariableDeclaration", "VariableDeclarator"], "sources": ["../../src/retrievers/getBindingIdentifiers.ts"], "sourcesContent": ["import {\n  isExportDeclaration,\n  isIdentifier,\n  isDeclaration,\n  isFunctionDeclaration,\n  isFunctionExpression,\n  isExportAllDeclaration,\n} from \"../validators/generated\";\nimport type * as t from \"..\";\n\nexport { getBindingIdentifiers as default };\n\nfunction getBindingIdentifiers(\n  node: t.Node,\n  duplicates: true,\n  outerOnly?: boolean,\n): Record<string, Array<t.Identifier>>;\n\nfunction getBindingIdentifiers(\n  node: t.Node,\n  duplicates?: false,\n  outerOnly?: boolean,\n): Record<string, t.Identifier>;\n\nfunction getBindingIdentifiers(\n  node: t.Node,\n  duplicates?: boolean,\n  outerOnly?: boolean,\n): Record<string, t.Identifier> | Record<string, Array<t.Identifier>>;\n\n/**\n * Return a list of binding identifiers associated with the input `node`.\n */\nfunction getBindingIdentifiers(\n  node: t.Node,\n  duplicates?: boolean,\n  outerOnly?: boolean,\n): Record<string, t.Identifier> | Record<string, Array<t.Identifier>> {\n  const search: t.Node[] = [].concat(node);\n  const ids = Object.create(null);\n\n  while (search.length) {\n    const id = search.shift();\n    if (!id) continue;\n\n    const keys =\n      // @ts-expect-error getBindingIdentifiers.keys do not cover all AST types\n      getBindingIdentifiers.keys[id.type];\n\n    if (isIdentifier(id)) {\n      if (duplicates) {\n        const _ids = (ids[id.name] = ids[id.name] || []);\n        _ids.push(id);\n      } else {\n        ids[id.name] = id;\n      }\n      continue;\n    }\n\n    if (isExportDeclaration(id) && !isExportAllDeclaration(id)) {\n      if (isDeclaration(id.declaration)) {\n        search.push(id.declaration);\n      }\n      continue;\n    }\n\n    if (outerOnly) {\n      if (isFunctionDeclaration(id)) {\n        search.push(id.id);\n        continue;\n      }\n\n      if (isFunctionExpression(id)) {\n        continue;\n      }\n    }\n\n    if (keys) {\n      for (let i = 0; i < keys.length; i++) {\n        const key = keys[i];\n        const nodes =\n          // @ts-expect-error key must present in id\n          id[key] as t.Node[] | t.Node | undefined | null;\n        if (nodes) {\n          Array.isArray(nodes) ? search.push(...nodes) : search.push(nodes);\n        }\n      }\n    }\n  }\n\n  // $FlowIssue Object.create() seems broken\n  return ids;\n}\n\n/**\n * Mapping of types to their identifier keys.\n */\ngetBindingIdentifiers.keys = {\n  DeclareClass: [\"id\"],\n  DeclareFunction: [\"id\"],\n  DeclareModule: [\"id\"],\n  DeclareVariable: [\"id\"],\n  DeclareInterface: [\"id\"],\n  DeclareTypeAlias: [\"id\"],\n  DeclareOpaqueType: [\"id\"],\n  InterfaceDeclaration: [\"id\"],\n  TypeAlias: [\"id\"],\n  OpaqueType: [\"id\"],\n\n  CatchClause: [\"param\"],\n  LabeledStatement: [\"label\"],\n  UnaryExpression: [\"argument\"],\n  AssignmentExpression: [\"left\"],\n\n  ImportSpecifier: [\"local\"],\n  ImportNamespaceSpecifier: [\"local\"],\n  ImportDefaultSpecifier: [\"local\"],\n  ImportDeclaration: [\"specifiers\"],\n\n  ExportSpecifier: [\"exported\"],\n  ExportNamespaceSpecifier: [\"exported\"],\n  ExportDefaultSpecifier: [\"exported\"],\n\n  FunctionDeclaration: [\"id\", \"params\"],\n  FunctionExpression: [\"id\", \"params\"],\n  ArrowFunctionExpression: [\"params\"],\n  ObjectMethod: [\"params\"],\n  ClassMethod: [\"params\"],\n  ClassPrivateMethod: [\"params\"],\n\n  ForInStatement: [\"left\"],\n  ForOfStatement: [\"left\"],\n\n  ClassDeclaration: [\"id\"],\n  ClassExpression: [\"id\"],\n\n  RestElement: [\"argument\"],\n  UpdateExpression: [\"argument\"],\n\n  ObjectProperty: [\"value\"],\n\n  AssignmentPattern: [\"left\"],\n  ArrayPattern: [\"elements\"],\n  ObjectPattern: [\"properties\"],\n\n  VariableDeclaration: [\"declarations\"],\n  VariableDeclarator: [\"id\"],\n};\n"], "mappings": ";;;;;;;AAAA;;AAiCA,SAASA,qBAAT,CACEC,IADF,EAEEC,UAFF,EAGEC,SAHF,EAIsE;EACpE,MAAMC,MAAgB,GAAG,GAAGC,MAAH,CAAUJ,IAAV,CAAzB;EACA,MAAMK,GAAG,GAAGC,MAAM,CAACC,MAAP,CAAc,IAAd,CAAZ;;EAEA,OAAOJ,MAAM,CAACK,MAAd,EAAsB;IACpB,MAAMC,EAAE,GAAGN,MAAM,CAACO,KAAP,EAAX;IACA,IAAI,CAACD,EAAL,EAAS;IAET,MAAME,IAAI,GAERZ,qBAAqB,CAACY,IAAtB,CAA2BF,EAAE,CAACG,IAA9B,CAFF;;IAIA,IAAI,IAAAC,uBAAA,EAAaJ,EAAb,CAAJ,EAAsB;MACpB,IAAIR,UAAJ,EAAgB;QACd,MAAMa,IAAI,GAAIT,GAAG,CAACI,EAAE,CAACM,IAAJ,CAAH,GAAeV,GAAG,CAACI,EAAE,CAACM,IAAJ,CAAH,IAAgB,EAA7C;;QACAD,IAAI,CAACE,IAAL,CAAUP,EAAV;MACD,CAHD,MAGO;QACLJ,GAAG,CAACI,EAAE,CAACM,IAAJ,CAAH,GAAeN,EAAf;MACD;;MACD;IACD;;IAED,IAAI,IAAAQ,8BAAA,EAAoBR,EAApB,KAA2B,CAAC,IAAAS,iCAAA,EAAuBT,EAAvB,CAAhC,EAA4D;MAC1D,IAAI,IAAAU,wBAAA,EAAcV,EAAE,CAACW,WAAjB,CAAJ,EAAmC;QACjCjB,MAAM,CAACa,IAAP,CAAYP,EAAE,CAACW,WAAf;MACD;;MACD;IACD;;IAED,IAAIlB,SAAJ,EAAe;MACb,IAAI,IAAAmB,gCAAA,EAAsBZ,EAAtB,CAAJ,EAA+B;QAC7BN,MAAM,CAACa,IAAP,CAAYP,EAAE,CAACA,EAAf;QACA;MACD;;MAED,IAAI,IAAAa,+BAAA,EAAqBb,EAArB,CAAJ,EAA8B;QAC5B;MACD;IACF;;IAED,IAAIE,IAAJ,EAAU;MACR,KAAK,IAAIY,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGZ,IAAI,CAACH,MAAzB,EAAiCe,CAAC,EAAlC,EAAsC;QACpC,MAAMC,GAAG,GAAGb,IAAI,CAACY,CAAD,CAAhB;QACA,MAAME,KAAK,GAEThB,EAAE,CAACe,GAAD,CAFJ;;QAGA,IAAIC,KAAJ,EAAW;UACTC,KAAK,CAACC,OAAN,CAAcF,KAAd,IAAuBtB,MAAM,CAACa,IAAP,CAAY,GAAGS,KAAf,CAAvB,GAA+CtB,MAAM,CAACa,IAAP,CAAYS,KAAZ,CAA/C;QACD;MACF;IACF;EACF;;EAGD,OAAOpB,GAAP;AACD;;AAKDN,qBAAqB,CAACY,IAAtB,GAA6B;EAC3BiB,YAAY,EAAE,CAAC,IAAD,CADa;EAE3BC,eAAe,EAAE,CAAC,IAAD,CAFU;EAG3BC,aAAa,EAAE,CAAC,IAAD,CAHY;EAI3BC,eAAe,EAAE,CAAC,IAAD,CAJU;EAK3BC,gBAAgB,EAAE,CAAC,IAAD,CALS;EAM3BC,gBAAgB,EAAE,CAAC,IAAD,CANS;EAO3BC,iBAAiB,EAAE,CAAC,IAAD,CAPQ;EAQ3BC,oBAAoB,EAAE,CAAC,IAAD,CARK;EAS3BC,SAAS,EAAE,CAAC,IAAD,CATgB;EAU3BC,UAAU,EAAE,CAAC,IAAD,CAVe;EAY3BC,WAAW,EAAE,CAAC,OAAD,CAZc;EAa3BC,gBAAgB,EAAE,CAAC,OAAD,CAbS;EAc3BC,eAAe,EAAE,CAAC,UAAD,CAdU;EAe3BC,oBAAoB,EAAE,CAAC,MAAD,CAfK;EAiB3BC,eAAe,EAAE,CAAC,OAAD,CAjBU;EAkB3BC,wBAAwB,EAAE,CAAC,OAAD,CAlBC;EAmB3BC,sBAAsB,EAAE,CAAC,OAAD,CAnBG;EAoB3BC,iBAAiB,EAAE,CAAC,YAAD,CApBQ;EAsB3BC,eAAe,EAAE,CAAC,UAAD,CAtBU;EAuB3BC,wBAAwB,EAAE,CAAC,UAAD,CAvBC;EAwB3BC,sBAAsB,EAAE,CAAC,UAAD,CAxBG;EA0B3BC,mBAAmB,EAAE,CAAC,IAAD,EAAO,QAAP,CA1BM;EA2B3BC,kBAAkB,EAAE,CAAC,IAAD,EAAO,QAAP,CA3BO;EA4B3BC,uBAAuB,EAAE,CAAC,QAAD,CA5BE;EA6B3BC,YAAY,EAAE,CAAC,QAAD,CA7Ba;EA8B3BC,WAAW,EAAE,CAAC,QAAD,CA9Bc;EA+B3BC,kBAAkB,EAAE,CAAC,QAAD,CA/BO;EAiC3BC,cAAc,EAAE,CAAC,MAAD,CAjCW;EAkC3BC,cAAc,EAAE,CAAC,MAAD,CAlCW;EAoC3BC,gBAAgB,EAAE,CAAC,IAAD,CApCS;EAqC3BC,eAAe,EAAE,CAAC,IAAD,CArCU;EAuC3BC,WAAW,EAAE,CAAC,UAAD,CAvCc;EAwC3BC,gBAAgB,EAAE,CAAC,UAAD,CAxCS;EA0C3BC,cAAc,EAAE,CAAC,OAAD,CA1CW;EA4C3BC,iBAAiB,EAAE,CAAC,MAAD,CA5CQ;EA6C3BC,YAAY,EAAE,CAAC,UAAD,CA7Ca;EA8C3BC,aAAa,EAAE,CAAC,YAAD,CA9CY;EAgD3BC,mBAAmB,EAAE,CAAC,cAAD,CAhDM;EAiD3BC,kBAAkB,EAAE,CAAC,IAAD;AAjDO,CAA7B"}