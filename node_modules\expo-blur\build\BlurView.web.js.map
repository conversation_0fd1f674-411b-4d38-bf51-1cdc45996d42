{"version": 3, "file": "BlurView.web.js", "sourceRoot": "", "sources": ["../src/BlurView.web.tsx"], "names": [], "mappings": "AAAA,mCAAmC;AACnC,YAAY,CAAC;AACb,OAAO,EAAE,UAAU,EAAE,mBAAmB,EAAE,MAAM,EAAE,MAAM,OAAO,CAAC;AAChE,OAAO,EAAE,IAAI,EAAE,MAAM,cAAc,CAAC;AAGpC,OAAO,kBAAkB,MAAM,sBAAsB,CAAC;AAEtD,MAAM,QAAQ,GAAG,UAAU,CACzB,CAAC,EAAE,IAAI,GAAG,SAAS,EAAE,SAAS,GAAG,EAAE,EAAE,KAAK,EAAE,GAAG,KAAK,EAAE,EAAE,GAAG,EAAE,EAAE;IAC7D,MAAM,WAAW,GAAG,MAAM,CAAiB,IAAI,CAAC,CAAC;IACjD,MAAM,SAAS,GAAG,YAAY,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC,CAAC;IAEpD,mBAAmB,CACjB,GAAG,EACH,GAAG,EAAE,CAAC,CAAC;QACL,cAAc,EAAE,CAAC,WAA0B,EAAE,EAAE;YAC7C,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,KAAK,EAAE,CAAC;gBAChC,OAAO;YACT,CAAC;YAED,kEAAkE;YAClE,MAAM,aAAa,GAAG,WAAW,CAAC,KAAK,EAAE,SAAS,IAAI,SAAS,CAAC;YAChE,MAAM,SAAS,GAAG,YAAY,CAAC,EAAE,SAAS,EAAE,aAAa,EAAE,IAAI,EAAE,IAAI,IAAI,SAAS,EAAE,CAAC,CAAC;YACtF,IAAI,WAAW,CAAC,KAAK,EAAE,CAAC;gBACtB,KAAK,MAAM,GAAG,IAAI,WAAW,CAAC,KAAK,EAAE,CAAC;oBACpC,IAAI,GAAG,KAAK,WAAW,EAAE,CAAC;wBACxB,WAAW,CAAC,OAAO,CAAC,KAAK,CAAC,GAAU,CAAC;4BACnC,WAAW,CAAC,KAAK,CAAC,GAAqC,CAAC,CAAC;oBAC7D,CAAC;gBACH,CAAC;YACH,CAAC;YAED,WAAW,CAAC,OAAO,CAAC,KAAK,CAAC,eAAe,GAAG,SAAS,CAAC,eAAe,CAAC;YACtE,WAAW,CAAC,OAAO,CAAC,KAAK,CAAC,cAAc,GAAG,SAAS,CAAC,cAAc,CAAC;YACpE,iGAAiG;YACjG,WAAW,CAAC,OAAO,CAAC,KAAK,CAAC,sBAAsB,CAAC,GAAG,SAAS,CAAC,oBAAoB,CAAC;QACrF,CAAC;KACF,CAAC,EACF,CAAC,SAAS,EAAE,IAAI,CAAC,CAClB,CAAC;IAEF,OAAO,CACL,CAAC,IAAI,CACH,IAAI,KAAK,CAAC,CACV,KAAK,CAAC,CAAC,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;IAC1B,sFAAsF;IACtF,GAAG,CAAC,CAAC,WAAW,CAAC,EACjB,CACH,CAAC;AACJ,CAAC,CACF,CAAC;AAEF,SAAS,YAAY,CAAC,EACpB,SAAS,EACT,IAAI,GACgD;IACpD,MAAM,IAAI,GAAG,uBAAuB,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE,GAAG,CAAC,GAAG,GAAG,KAAK,CAAC;IACxE,OAAO;QACL,eAAe,EAAE,kBAAkB,CAAC,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE,GAAG,CAAC,EAAE,IAAI,CAAC;QACnE,cAAc,EAAE,IAAI;QACpB,oBAAoB,EAAE,IAAI;KAC3B,CAAC;AACJ,CAAC;AAED,eAAe,QAAQ,CAAC", "sourcesContent": ["// Copyright © 2024 650 Industries.\n'use client';\nimport { forwardRef, useImperativeHandle, useRef } from 'react';\nimport { View } from 'react-native';\n\nimport { BlurViewProps } from './BlurView.types';\nimport getBackgroundColor from './getBackgroundColor';\n\nconst BlurView = forwardRef<{ setNativeProps: (props: BlurViewProps) => void }, BlurViewProps>(\n  ({ tint = 'default', intensity = 50, style, ...props }, ref) => {\n    const blurViewRef = useRef<HTMLDivElement>(null);\n    const blurStyle = getBlurStyle({ tint, intensity });\n\n    useImperativeHandle(\n      ref,\n      () => ({\n        setNativeProps: (nativeProps: BlurViewProps) => {\n          if (!blurViewRef.current?.style) {\n            return;\n          }\n\n          // @ts-expect-error: `style.intensity` is not defined in the types\n          const nextIntensity = nativeProps.style?.intensity ?? intensity;\n          const blurStyle = getBlurStyle({ intensity: nextIntensity, tint: tint ?? 'default' });\n          if (nativeProps.style) {\n            for (const key in nativeProps.style) {\n              if (key !== 'intensity') {\n                blurViewRef.current.style[key as any] =\n                  nativeProps.style[key as keyof typeof nativeProps.style];\n              }\n            }\n          }\n\n          blurViewRef.current.style.backgroundColor = blurStyle.backgroundColor;\n          blurViewRef.current.style.backdropFilter = blurStyle.backdropFilter;\n          // @ts-expect-error: Webkit-specific legacy property (let's not type this, since it's deprecated)\n          blurViewRef.current.style['webkitBackdropFilter'] = blurStyle.WebkitBackdropFilter;\n        },\n      }),\n      [intensity, tint]\n    );\n\n    return (\n      <View\n        {...props}\n        style={[style, blurStyle]}\n        /** @ts-expect-error: mismatch in ref type to support manually setting style props. */\n        ref={blurViewRef}\n      />\n    );\n  }\n);\n\nfunction getBlurStyle({\n  intensity,\n  tint,\n}: Required<Pick<BlurViewProps, 'intensity' | 'tint'>>): Record<string, string> {\n  const blur = `saturate(180%) blur(${Math.min(intensity, 100) * 0.2}px)`;\n  return {\n    backgroundColor: getBackgroundColor(Math.min(intensity, 100), tint),\n    backdropFilter: blur,\n    WebkitBackdropFilter: blur,\n  };\n}\n\nexport default BlurView;\n"]}