// API Configuration
// To use real AI responses, add your OpenAI API key here

export const API_CONFIG = {
  // Replace with your actual OpenAI API key
  // Get one from: https://platform.openai.com/api-keys
  OPENAI_API_KEY: 'YOUR_OPENAI_API_KEY_HERE',
  
  // OpenAI API endpoint
  OPENAI_ENDPOINT: 'https://api.openai.com/v1/chat/completions',
  
  // Model to use
  MODEL: 'gpt-4',
  
  // Maximum tokens for response
  MAX_TOKENS: 1000,
  
  // Temperature for response creativity (0.0 to 1.0)
  TEMPERATURE: 0.3
}

// Instructions for getting an API key:
// 1. Go to https://platform.openai.com/
// 2. Sign up or log in
// 3. Go to API Keys section
// 4. Create a new API key
// 5. Replace 'YOUR_OPENAI_API_KEY_HERE' with your actual key
// 6. Make sure to keep your API key secure and never commit it to version control

export default API_CONFIG
