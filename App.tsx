import React, { useState, useEffect } from "react"
import { View, Text, StyleSheet, Dimensions, Animated, StatusBar, Platform } from "react-native"
import { SafeAreaProvider, SafeAreaView } from "react-native-safe-area-context"
import { LinearGradient } from "expo-linear-gradient"
import { BlurView } from "expo-blur"
import { Ionicons } from "@expo/vector-icons"

import AskAIScreen from "./screens/AskAIScreen"
import JournalScreen from "./screens/JournalScreen"
import { JournalProvider } from "./context/JournalContext"
import FuturisticTabBar from "./components/FuturisticTabBar"

const { width, height } = Dimensions.get('window')

const FuturisticApp: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'ai' | 'journal'>('ai')
  const [animatedValue] = useState(new Animated.Value(0))
  const [splitView, setSplitView] = useState(width > 768) // Enable split view on tablets/desktop

  useEffect(() => {
    // Animated background gradient
    const animation = Animated.loop(
      Animated.sequence([
        Animated.timing(animatedValue, {
          toValue: 1,
          duration: 3000,
          useNativeDriver: false,
        }),
        Animated.timing(animatedValue, {
          toValue: 0,
          duration: 3000,
          useNativeDriver: false,
        }),
      ])
    )
    animation.start()
  }, [])

  const backgroundInterpolation = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: ['rgba(99, 102, 241, 0.1)', 'rgba(139, 92, 246, 0.1)']
  })

  return (
    <SafeAreaProvider>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent />
      <JournalProvider>
        <View style={styles.container}>
          {/* Animated Background */}
          <Animated.View style={[styles.animatedBackground, { backgroundColor: backgroundInterpolation }]} />

          {/* Gradient Overlay */}
          <LinearGradient
            colors={['rgba(15, 23, 42, 0.95)', 'rgba(30, 41, 59, 0.9)', 'rgba(51, 65, 85, 0.85)']}
            style={styles.gradientOverlay}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
          />

          <SafeAreaView style={styles.safeArea}>
            {/* Futuristic Header */}
            <View style={styles.header}>
              <LinearGradient
                colors={['rgba(99, 102, 241, 0.2)', 'rgba(139, 92, 246, 0.2)']}
                style={styles.headerGradient}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
              />
              <BlurView intensity={20} style={styles.headerBlur}>
                <View style={styles.headerContent}>
                  <View style={styles.logoContainer}>
                    <LinearGradient
                      colors={['#6366F1', '#8B5CF6', '#EC4899']}
                      style={styles.logoGradient}
                    >
                      <Ionicons name="library" size={24} color="#FFFFFF" />
                    </LinearGradient>
                    <Text style={styles.logoText}>LegalAI</Text>
                    <View style={styles.betaBadge}>
                      <Text style={styles.betaText}>QUANTUM</Text>
                    </View>
                  </View>
                  <View style={styles.statusIndicator}>
                    <View style={styles.onlineIndicator} />
                    <Text style={styles.statusText}>ONLINE</Text>
                  </View>
                </View>
              </BlurView>
            </View>

            {/* Main Content Area */}
            <View style={styles.mainContent}>
              {splitView ? (
                // Split View for larger screens
                <View style={styles.splitContainer}>
                  <View style={styles.leftPanel}>
                    <AskAIScreen />
                  </View>
                  <View style={styles.divider} />
                  <View style={styles.rightPanel}>
                    <JournalScreen />
                  </View>
                </View>
              ) : (
                // Single View for mobile
                <View style={styles.singleContainer}>
                  {activeTab === 'ai' ? <AskAIScreen /> : <JournalScreen />}
                </View>
              )}
            </View>

            {/* Futuristic Tab Bar */}
            <FuturisticTabBar
              activeTab={activeTab}
              onTabChange={setActiveTab}
              splitView={splitView}
              onToggleSplitView={() => setSplitView(!splitView)}
            />
          </SafeAreaView>
        </View>
      </JournalProvider>
    </SafeAreaProvider>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#0F172A',
  },
  animatedBackground: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  gradientOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  safeArea: {
    flex: 1,
  },
  header: {
    height: 80,
    position: 'relative',
  },
  headerGradient: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  headerBlur: {
    flex: 1,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(99, 102, 241, 0.3)',
  },
  headerContent: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
  },
  logoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  logoGradient: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  logoText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginRight: 8,
  },
  betaBadge: {
    backgroundColor: 'rgba(236, 72, 153, 0.2)',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: '#EC4899',
  },
  betaText: {
    fontSize: 10,
    fontWeight: 'bold',
    color: '#EC4899',
  },
  statusIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  onlineIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#10B981',
    marginRight: 6,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#10B981',
  },
  mainContent: {
    flex: 1,
  },
  splitContainer: {
    flex: 1,
    flexDirection: 'row',
  },
  leftPanel: {
    flex: 1,
    marginRight: 1,
  },
  rightPanel: {
    flex: 1,
    marginLeft: 1,
  },
  divider: {
    width: 2,
    backgroundColor: 'rgba(99, 102, 241, 0.3)',
    marginVertical: 10,
  },
  singleContainer: {
    flex: 1,
  },
})

export default FuturisticApp
