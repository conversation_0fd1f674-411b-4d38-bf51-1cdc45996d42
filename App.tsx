import { NavigationContainer } from "@react-navigation/native"
import { createBottomTabNavigator } from "@react-navigation/bottom-tabs"
import { SafeAreaProvider } from "react-native-safe-area-context"
import { StatusBar, StyleSheet } from "react-native"
import { Ionicons } from "@expo/vector-icons"

import AskAIScreen from "./screens/AskAIScreen"
import JournalScreen from "./screens/JournalScreen"
import { JournalProvider } from "./context/JournalContext"

const Tab = createBottomTabNavigator()

export default function App() {
  return (
    <SafeAreaProvider>
      <StatusBar barStyle="dark-content" backgroundColor="#F8FAFC" />
      <JournalProvider>
        <NavigationContainer>
          <Tab.Navigator
            screenOptions={({ route }) => ({
              tabBarIcon: ({ focused, color, size }) => {
                let iconName

                if (route.name === "Ask AI") {
                  iconName = focused ? "chatbubble-ellipses" : "chatbubble-ellipses-outline"
                } else if (route.name === "Journal") {
                  iconName = focused ? "journal" : "journal-outline"
                }

                return <Ionicons name={iconName} size={size} color={color} />
              },
              tabBarActiveTintColor: "#6366F1",
              tabBarInactiveTintColor: "#9CA3AF",
              headerShown: false,
              tabBarStyle: {
                paddingVertical: 12,
                height: 65,
                backgroundColor: "#FFFFFF",
                borderTopWidth: 0,
                shadowColor: "#000",
                shadowOffset: {
                  width: 0,
                  height: -2,
                },
                shadowOpacity: 0.1,
                shadowRadius: 8,
                elevation: 8,
              },
              tabBarLabelStyle: {
                fontSize: 12,
                fontWeight: "600",
                paddingBottom: 5,
              },
            })}
          >
            <Tab.Screen name="Ask AI" component={AskAIScreen} />
            <Tab.Screen name="Journal" component={JournalScreen} />
          </Tab.Navigator>
        </NavigationContainer>
      </JournalProvider>
    </SafeAreaProvider>
  )
}
