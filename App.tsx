import { NavigationContainer } from "@react-navigation/native"
import { createBottomTabNavigator } from "@react-navigation/bottom-tabs"
import { SafeAreaProvider } from "react-native-safe-area-context"
import { StatusBar, StyleSheet } from "react-native"
import { Ionicons } from "@expo/vector-icons"

import AskAIScreen from "./screens/AskAIScreen"
import JournalScreen from "./screens/JournalScreen"
import { JournalProvider } from "./context/JournalContext"

const Tab = createBottomTabNavigator()

export default function App() {
  return (
    <SafeAreaProvider>
      <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />
      <JournalProvider>
        <NavigationContainer>
          <Tab.Navigator
            screenOptions={({ route }) => ({
              tabBarIcon: ({ focused, color, size }) => {
                let iconName

                if (route.name === "Ask AI") {
                  iconName = focused ? "chatbubble" : "chatbubble-outline"
                } else if (route.name === "Journal") {
                  iconName = focused ? "journal" : "journal-outline"
                }

                return <Ionicons name={iconName} size={size} color={color} />
              },
              tabBarActiveTintColor: "#4F46E5",
              tabBarInactiveTintColor: "#6B7280",
              headerShown: false,
              tabBarStyle: {
                paddingVertical: 10,
                height: 60,
                backgroundColor: "#FFFFFF",
                borderTopWidth: 1,
                borderTopColor: "#E5E7EB",
              },
              tabBarLabelStyle: {
                fontSize: 12,
                fontWeight: "500",
                paddingBottom: 5,
              },
            })}
          >
            <Tab.Screen name="Ask AI" component={AskAIScreen} />
            <Tab.Screen name="Journal" component={JournalScreen} />
          </Tab.Navigator>
        </NavigationContainer>
      </JournalProvider>
    </SafeAreaProvider>
  )
}
