{"name": "expo-blur", "version": "14.1.4", "description": "A component that renders a native blur view on iOS and falls back to a semi-transparent view on Android. A common usage of this is for navigation bars, tab bars, and modals.", "main": "build/index.js", "types": "build/index.d.ts", "sideEffects": false, "scripts": {"build": "expo-module build", "clean": "expo-module clean", "lint": "expo-module lint", "test": "expo-module test", "prepare": "expo-module prepare", "prepublishOnly": "expo-module prepublishOnly", "expo-module": "expo-module"}, "jest": {"preset": "expo-module-scripts"}, "keywords": ["react-native", "expo", "expo-blur"], "repository": {"type": "git", "url": "https://github.com/expo/expo.git", "directory": "packages/expo-blur"}, "bugs": {"url": "https://github.com/expo/expo/issues"}, "author": "650 Industries, Inc.", "license": "MIT", "homepage": "https://docs.expo.dev/versions/latest/sdk/blur-view/", "dependencies": {}, "devDependencies": {"@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^15.0.7", "@testing-library/react-native": "^13.1.0", "expo-module-scripts": "^4.1.6"}, "peerDependencies": {"expo": "*", "react": "*", "react-native": "*"}, "gitHead": "84355076bc31a356aa3d23257f387f221885f53d"}