import React, { useState, useEffect } from 'react'
import { View, Text, TouchableOpacity, StyleSheet, Animated, Dimensions } from 'react-native'
import { LinearGradient } from 'expo-linear-gradient'
import { BlurView } from 'expo-blur'
import { Ionicons } from '@expo/vector-icons'

const { width } = Dimensions.get('window')

interface FuturisticTabBarProps {
  activeTab: 'ai' | 'journal'
  onTabChange: (tab: 'ai' | 'journal') => void
  splitView: boolean
  onToggleSplitView: () => void
}

const FuturisticTabBar: React.FC<FuturisticTabBarProps> = ({
  activeTab,
  onTabChange,
  splitView,
  onToggleSplitView,
}) => {
  const [animatedValue] = useState(new Animated.Value(0))
  const [pulseAnim] = useState(new Animated.Value(1))

  useEffect(() => {
    // Continuous glow animation
    const glowAnimation = Animated.loop(
      Animated.sequence([
        Animated.timing(animatedValue, {
          toValue: 1,
          duration: 2000,
          useNativeDriver: false,
        }),
        Animated.timing(animatedValue, {
          toValue: 0,
          duration: 2000,
          useNativeDriver: false,
        }),
      ])
    )
    glowAnimation.start()

    // Pulse animation for active elements
    const pulseAnimation = Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1.1,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
      ])
    )
    pulseAnimation.start()
  }, [])

  const glowInterpolation = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: ['rgba(99, 102, 241, 0.3)', 'rgba(139, 92, 246, 0.6)']
  })

  return (
    <View style={styles.container}>
      {/* Animated Glow Background */}
      <Animated.View style={[styles.glowBackground, { backgroundColor: glowInterpolation }]} />
      
      {/* Blur Effect */}
      <BlurView intensity={30} style={styles.blurContainer}>
        <LinearGradient
          colors={['rgba(15, 23, 42, 0.8)', 'rgba(30, 41, 59, 0.9)']}
          style={styles.gradientBackground}
        >
          <View style={styles.tabContainer}>
            {/* AI Tab */}
            <TouchableOpacity
              style={[styles.tab, activeTab === 'ai' && styles.activeTab]}
              onPress={() => onTabChange('ai')}
              activeOpacity={0.8}
            >
              <Animated.View style={[
                styles.tabContent,
                activeTab === 'ai' && { transform: [{ scale: pulseAnim }] }
              ]}>
                <LinearGradient
                  colors={activeTab === 'ai' 
                    ? ['#6366F1', '#8B5CF6'] 
                    : ['rgba(99, 102, 241, 0.3)', 'rgba(139, 92, 246, 0.3)']}
                  style={styles.iconContainer}
                >
                  <Ionicons 
                    name="sparkles" 
                    size={24} 
                    color={activeTab === 'ai' ? '#FFFFFF' : '#94A3B8'} 
                  />
                </LinearGradient>
                <Text style={[styles.tabText, activeTab === 'ai' && styles.activeTabText]}>
                  AI ASSISTANT
                </Text>
                {activeTab === 'ai' && (
                  <View style={styles.activeIndicator}>
                    <LinearGradient
                      colors={['#6366F1', '#8B5CF6', '#EC4899']}
                      style={styles.indicatorGradient}
                    />
                  </View>
                )}
              </Animated.View>
            </TouchableOpacity>

            {/* Split View Toggle */}
            <TouchableOpacity
              style={styles.splitToggle}
              onPress={onToggleSplitView}
              activeOpacity={0.8}
            >
              <LinearGradient
                colors={splitView 
                  ? ['#10B981', '#059669'] 
                  : ['rgba(16, 185, 129, 0.3)', 'rgba(5, 150, 105, 0.3)']}
                style={styles.splitIconContainer}
              >
                <Ionicons 
                  name={splitView ? "grid" : "grid-outline"} 
                  size={20} 
                  color={splitView ? '#FFFFFF' : '#94A3B8'} 
                />
              </LinearGradient>
            </TouchableOpacity>

            {/* Journal Tab */}
            <TouchableOpacity
              style={[styles.tab, activeTab === 'journal' && styles.activeTab]}
              onPress={() => onTabChange('journal')}
              activeOpacity={0.8}
            >
              <Animated.View style={[
                styles.tabContent,
                activeTab === 'journal' && { transform: [{ scale: pulseAnim }] }
              ]}>
                <LinearGradient
                  colors={activeTab === 'journal' 
                    ? ['#6366F1', '#8B5CF6'] 
                    : ['rgba(99, 102, 241, 0.3)', 'rgba(139, 92, 246, 0.3)']}
                  style={styles.iconContainer}
                >
                  <Ionicons 
                    name="library" 
                    size={24} 
                    color={activeTab === 'journal' ? '#FFFFFF' : '#94A3B8'} 
                  />
                </LinearGradient>
                <Text style={[styles.tabText, activeTab === 'journal' && styles.activeTabText]}>
                  JOURNAL
                </Text>
                {activeTab === 'journal' && (
                  <View style={styles.activeIndicator}>
                    <LinearGradient
                      colors={['#6366F1', '#8B5CF6', '#EC4899']}
                      style={styles.indicatorGradient}
                    />
                  </View>
                )}
              </Animated.View>
            </TouchableOpacity>
          </View>

          {/* Neural Network Pattern */}
          <View style={styles.neuralPattern}>
            {[...Array(5)].map((_, i) => (
              <Animated.View
                key={i}
                style={[
                  styles.neuralDot,
                  {
                    opacity: animatedValue,
                    left: (i * width) / 6,
                  }
                ]}
              />
            ))}
          </View>
        </LinearGradient>
      </BlurView>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    height: 90,
    position: 'relative',
  },
  glowBackground: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  blurContainer: {
    flex: 1,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    overflow: 'hidden',
  },
  gradientBackground: {
    flex: 1,
    borderTopWidth: 1,
    borderTopColor: 'rgba(99, 102, 241, 0.3)',
  },
  tabContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-around',
    paddingHorizontal: 20,
    paddingTop: 10,
  },
  tab: {
    flex: 1,
    alignItems: 'center',
  },
  activeTab: {
    // Additional styling for active tab if needed
  },
  tabContent: {
    alignItems: 'center',
  },
  iconContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 6,
  },
  splitToggle: {
    alignItems: 'center',
    marginHorizontal: 10,
  },
  splitIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  tabText: {
    fontSize: 10,
    fontWeight: 'bold',
    color: '#94A3B8',
    letterSpacing: 1,
  },
  activeTabText: {
    color: '#FFFFFF',
  },
  activeIndicator: {
    position: 'absolute',
    bottom: -15,
    width: 30,
    height: 3,
    borderRadius: 2,
  },
  indicatorGradient: {
    flex: 1,
    borderRadius: 2,
  },
  neuralPattern: {
    position: 'absolute',
    bottom: 5,
    left: 0,
    right: 0,
    height: 2,
  },
  neuralDot: {
    position: 'absolute',
    width: 4,
    height: 4,
    borderRadius: 2,
    backgroundColor: '#6366F1',
  },
})

export default FuturisticTabBar
