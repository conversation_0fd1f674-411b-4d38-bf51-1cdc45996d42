# PostCSS Nested

<img align="right" width="135" height="95"
     title="Philosopher’s stone, logo of PostCSS"
     src="https://postcss.org/logo-leftp.svg">

[PostCSS] plugin to unwrap nested rules like how Sass does it.

```css
.phone {
    &_title {
        width: 500px;
        @media (max-width: 500px) {
            width: auto;
        }
        body.is_dark & {
            color: white;
        }
    }
    img {
        display: block;
    }
}

.title {
  font-size: var(--font);

  @at-root html {
      --font: 16px
  }
}
```

will be processed to:

```css
.phone_title {
    width: 500px;
}
@media (max-width: 500px) {
    .phone_title {
        width: auto;
    }
}
body.is_dark .phone_title {
    color: white;
}
.phone img {
    display: block;
}

.title {
  font-size: var(--font);
}
html {
  --font: 16px
}
```

Related plugins:

* Use [`postcss-atroot`] for `@at-root` at-rule to move nested child
  to the CSS root.
* Use [`postcss-current-selector`] **after** this plugin if you want
  to use current selector in properties or variables values.
* Use [`postcss-nested-ancestors`] **before** this plugin if you want
  to reference any ancestor element directly in your selectors with `^&`.

Alternatives:

* See also [`postcss-nesting`], which implements [CSSWG draft]
  (requires the `&` and introduces `@nest`).
* [`postcss-nested-props`] for nested properties like `font-size`.

<a href="https://evilmartians.com/?utm_source=postcss-nested">
  <img src="https://evilmartians.com/badges/sponsored-by-evil-martians.svg"
       alt="Sponsored by Evil Martians" width="236" height="54">
</a>

[`postcss-atroot`]: https://github.com/OEvgeny/postcss-atroot
[`postcss-current-selector`]: https://github.com/komlev/postcss-current-selector
[`postcss-nested-ancestors`]: https://github.com/toomuchdesign/postcss-nested-ancestors
[`postcss-nested-props`]:     https://github.com/jedmao/postcss-nested-props
[`postcss-nesting`]:          https://github.com/jonathantneal/postcss-nesting
[CSSWG draft]:              https://drafts.csswg.org/css-nesting-1/
[PostCSS]:                  https://github.com/postcss/postcss


## Usage

**Step 1:** Install plugin:

```sh
npm install --save-dev postcss postcss-nested
```

**Step 2:** Check your project for existing PostCSS config: `postcss.config.js`
in the project root, `"postcss"` section in `package.json`
or `postcss` in bundle config.

If you do not use PostCSS, add it according to [official docs]
and set this plugin in settings.

**Step 3:** Add the plugin to plugins list:

```diff
module.exports = {
  plugins: [
+   require('postcss-nested'),
    require('autoprefixer')
  ]
}
```

[official docs]: https://github.com/postcss/postcss#usage


## Options

### `bubble`

By default, plugin will bubble only `@media` and `@supports` at-rules.
You can add your custom at-rules to this list by `bubble` option:

```js
postcss([ require('postcss-nested')({ bubble: ['phone'] }) ])
```

```css
/* input */
a {
  color: white;
  @phone {
    color: black;
  }
}
/* output */
a {
  color: white;
}
@phone {
  a {
    color: black;
  }
}
```


### `unwrap`

By default, plugin will unwrap only `@font-face`, `@keyframes` and `@document`
at-rules. You can add your custom at-rules to this list by `unwrap` option:

```js
postcss([ require('postcss-nested')({ unwrap: ['phone'] }) ])
```

```css
/* input */
a {
  color: white;
  @phone {
    color: black;
  }
}
/* output */
a {
  color: white;
}
@phone {
  color: black;
}
```


### `preserveEmpty`

By default, plugin will strip out any empty selector generated by intermediate
nesting levels. You can set `preserveEmpty` to `true` to preserve them.

```css
.a {
    .b {
        color: black;
    }
}
```

Will be compiled to:

```css
.a { }
.a .b {
    color: black;
}
```

This is especially useful if you want to export the empty classes with `postcss-modules`.
