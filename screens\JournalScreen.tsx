import type React from "react"
import { useState, useEffect } from "react"
import { View, Text, FlatList, TouchableOpacity, Alert, StyleSheet, Dimensions, Animated } from "react-native"
import { LinearGradient } from "expo-linear-gradient"
import { BlurView } from "expo-blur"
import { Ionicons } from "@expo/vector-icons"
import { useJournal } from "../context/JournalContext"
import type { JournalEntry } from "../types"

const { width } = Dimensions.get('window')

const JournalScreen: React.FC = () => {
  const { entries, removeEntry } = useJournal()
  const [animatedValue] = useState(new Animated.Value(0))
  const [pulseAnim] = useState(new Animated.Value(1))

  useEffect(() => {
    // Continuous background animation
    const backgroundAnimation = Animated.loop(
      Animated.sequence([
        Animated.timing(animatedValue, {
          toValue: 1,
          duration: 5000,
          useNativeDriver: false,
        }),
        Animated.timing(animatedValue, {
          toValue: 0,
          duration: 5000,
          useNativeDriver: false,
        }),
      ])
    )
    backgroundAnimation.start()

    // Pulse animation for interactive elements
    const pulseAnimation = Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1.02,
          duration: 3000,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 3000,
          useNativeDriver: true,
        }),
      ])
    )
    pulseAnimation.start()
  }, [])

  const formatDate = (dateString: string): string => {
    const date = new Date(dateString)
    return date.toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric",
    })
  }

  const handleDelete = (id: string) => {
    Alert.alert("Delete Entry", "Are you sure you want to delete this journal entry?", [
      { text: "Cancel", style: "cancel" },
      { text: "Delete", onPress: () => removeEntry(id), style: "destructive" },
    ])
  }

  const renderItem = ({ item, index }: { item: JournalEntry; index: number }) => (
    <Animated.View style={[styles.entryCard, { transform: [{ scale: pulseAnim }] }]}>
      <BlurView intensity={30} style={styles.entryBlur}>
        <LinearGradient
          colors={['rgba(15, 23, 42, 0.9)', 'rgba(30, 41, 59, 0.8)']}
          style={styles.entryGradient}
        >
          <View style={styles.entryHeader}>
            <View style={styles.entryHeaderLeft}>
              <LinearGradient
                colors={['#6366F1', '#8B5CF6']}
                style={styles.tagContainer}
              >
                <Ionicons name="shield-checkmark" size={10} color="#FFFFFF" />
                <Text style={styles.tagText}>{item.tag}</Text>
              </LinearGradient>
              <View style={styles.entryIndex}>
                <Text style={styles.indexText}>#{String(index + 1).padStart(3, '0')}</Text>
              </View>
            </View>
            <Text style={styles.dateText}>{formatDate(item.date)}</Text>
          </View>

          <View style={styles.entryContent}>
            <Text style={styles.questionText}>{item.question}</Text>
            <Text style={styles.answerText} numberOfLines={3}>
              {item.answer}
            </Text>
          </View>

          <View style={styles.entryActions}>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => Alert.alert("Edit", "Edit functionality coming soon...")}
              activeOpacity={0.8}
            >
              <LinearGradient
                colors={['rgba(99, 102, 241, 0.2)', 'rgba(139, 92, 246, 0.2)']}
                style={styles.actionGradient}
              >
                <Ionicons name="create" size={14} color="#6366F1" />
                <Text style={styles.actionText}>Edit</Text>
              </LinearGradient>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => handleDelete(item.id)}
              activeOpacity={0.8}
            >
              <LinearGradient
                colors={['rgba(239, 68, 68, 0.2)', 'rgba(220, 38, 38, 0.2)']}
                style={styles.actionGradient}
              >
                <Ionicons name="trash" size={14} color="#EF4444" />
                <Text style={styles.deleteText}>Delete</Text>
              </LinearGradient>
            </TouchableOpacity>
          </View>
        </LinearGradient>
      </BlurView>
    </Animated.View>
  )

  const backgroundInterpolation = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: ['rgba(139, 92, 246, 0.05)', 'rgba(99, 102, 241, 0.1)']
  })

  return (
    <View style={styles.container}>
      {/* Animated Background */}
      <Animated.View style={[styles.animatedBackground, { backgroundColor: backgroundInterpolation }]} />

      {/* Futuristic Header */}
      <View style={styles.header}>
        <LinearGradient
          colors={['rgba(139, 92, 246, 0.1)', 'rgba(99, 102, 241, 0.05)']}
          style={styles.headerGradient}
        >
          <Animated.View style={[styles.headerContent, { transform: [{ scale: pulseAnim }] }]}>
            <LinearGradient
              colors={['#8B5CF6', '#6366F1', '#EC4899']}
              style={styles.headerIcon}
            >
              <Ionicons name="journal" size={32} color="#FFFFFF" />
            </LinearGradient>
            <Text style={styles.title}>Legal Journal</Text>
            <Text style={styles.subtitle}>Your saved legal questions and answers</Text>

            {entries.length > 0 && (
              <View style={styles.statsContainer}>
                <View style={styles.statsBadge}>
                  <View style={styles.statsIndicator} />
                  <Text style={styles.statsText}>{entries.length} entries saved</Text>
                </View>
              </View>
            )}
          </Animated.View>
        </LinearGradient>
      </View>

      {entries.length === 0 ? (
        <View style={styles.emptyState}>
          <LinearGradient
            colors={['rgba(99, 102, 241, 0.1)', 'rgba(139, 92, 246, 0.05)']}
            style={styles.emptyGradient}
          >
            <View style={styles.emptyIcon}>
              <LinearGradient
                colors={['#6366F1', '#8B5CF6']}
                style={styles.emptyIconGradient}
              >
                <Ionicons name="cloud-upload" size={48} color="#FFFFFF" />
              </LinearGradient>
            </View>
            <Text style={styles.emptyTitle}>Journal is Empty</Text>
            <Text style={styles.emptySubtitle}>
              Save legal questions from the AI Assistant to see them here
            </Text>
          </LinearGradient>
        </View>
      ) : (
        <FlatList
          data={entries}
          renderItem={renderItem}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.listContainer}
          showsVerticalScrollIndicator={false}
        />
      )}
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'transparent',
  },
  animatedBackground: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  header: {
    marginBottom: 20,
    borderRadius: 20,
    overflow: 'hidden',
    marginHorizontal: 16,
  },
  headerGradient: {
    padding: 24,
  },
  headerContent: {
    alignItems: 'center',
  },
  headerIcon: {
    width: 80,
    height: 80,
    borderRadius: 40,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
    shadowColor: '#8B5CF6',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.4,
    shadowRadius: 16,
    elevation: 12,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#FFFFFF',
    textAlign: 'center',
    marginBottom: 8,
    letterSpacing: 2,
  },
  subtitle: {
    fontSize: 14,
    color: '#94A3B8',
    textAlign: 'center',
    marginBottom: 16,
    letterSpacing: 1,
  },
  statsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  statsBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(16, 185, 129, 0.2)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: '#10B981',
  },
  statsIndicator: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: '#10B981',
    marginRight: 6,
  },
  statsText: {
    fontSize: 10,
    fontWeight: 'bold',
    color: '#10B981',
    letterSpacing: 1,
  },

  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 16,
  },
  emptyGradient: {
    width: '100%',
    alignItems: 'center',
    paddingVertical: 60,
    paddingHorizontal: 40,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: 'rgba(99, 102, 241, 0.3)',
  },
  emptyIcon: {
    marginBottom: 24,
  },
  emptyIconGradient: {
    width: 100,
    height: 100,
    borderRadius: 50,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#6366F1',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.4,
    shadowRadius: 16,
    elevation: 12,
  },
  emptyTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FFFFFF',
    textAlign: 'center',
    marginBottom: 12,
    letterSpacing: 2,
  },
  emptySubtitle: {
    fontSize: 14,
    color: '#94A3B8',
    textAlign: 'center',
    lineHeight: 22,
    marginBottom: 24,
  },

  listContainer: {
    padding: 16,
    paddingBottom: 40,
  },
  entryCard: {
    marginBottom: 16,
    borderRadius: 20,
    overflow: 'hidden',
  },
  entryBlur: {
    borderRadius: 20,
    overflow: 'hidden',
  },
  entryGradient: {
    padding: 20,
    borderWidth: 1,
    borderColor: 'rgba(99, 102, 241, 0.3)',
  },
  entryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  entryHeaderLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  tagContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 16,
    marginRight: 12,
  },
  tagText: {
    fontSize: 9,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginLeft: 4,
    letterSpacing: 1,
  },
  entryIndex: {
    backgroundColor: 'rgba(139, 92, 246, 0.2)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#8B5CF6',
  },
  indexText: {
    fontSize: 9,
    fontWeight: 'bold',
    color: '#8B5CF6',
    letterSpacing: 1,
  },
  dateText: {
    fontSize: 11,
    color: '#64748B',
    fontWeight: '500',
  },
  entryContent: {
    marginBottom: 16,
  },
  questionText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
    marginBottom: 8,
    lineHeight: 22,
  },
  answerText: {
    fontSize: 14,
    color: '#94A3B8',
    lineHeight: 20,
  },
  entryActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
    gap: 12,
  },
  actionButton: {
    borderRadius: 10,
    overflow: 'hidden',
  },
  actionGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderWidth: 1,
    borderColor: 'rgba(99, 102, 241, 0.3)',
  },
  actionText: {
    fontSize: 10,
    color: '#6366F1',
    marginLeft: 4,
    fontWeight: 'bold',
    letterSpacing: 1,
  },
  deleteText: {
    fontSize: 10,
    color: '#EF4444',
    marginLeft: 4,
    fontWeight: 'bold',
    letterSpacing: 1,
  },
})

export default JournalScreen
