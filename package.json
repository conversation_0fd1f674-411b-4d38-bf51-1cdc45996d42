{"name": "legal-buddy", "version": "1.0.0", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@react-navigation/bottom-tabs": "^6.5.11", "@react-navigation/native": "^6.1.9", "expo": "^53.0.0", "expo-status-bar": "~2.2.3", "nativewind": "^4.1.23", "react": "19.0.0", "react-native": "0.79.2", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~19.0.0", "autoprefixer": "^10.4.21", "metro": "^0.82.4", "metro-config": "^0.82.4", "postcss": "^8.5.4", "tailwindcss": "^3.3.2", "tailwindcss-animate": "^1.0.7", "typescript": "^5.1.3"}, "private": true}