{"version": 3, "names": ["getOuterBindingIdentifiers", "node", "duplicates", "getBindingIdentifiers"], "sources": ["../../src/retrievers/getOuterBindingIdentifiers.ts"], "sourcesContent": ["import getBindingIdentifiers from \"./getBindingIdentifiers\";\nimport type * as t from \"..\";\n\nexport default getOuterBindingIdentifiers as {\n  (node: t.Node, duplicates: true): Record<string, Array<t.Identifier>>;\n  (node: t.Node, duplicates?: false): Record<string, t.Identifier>;\n  (node: t.Node, duplicates?: boolean):\n    | Record<string, t.Identifier>\n    | Record<string, Array<t.Identifier>>;\n};\n\nfunction getOuterBindingIdentifiers(\n  node: t.Node,\n  duplicates: boolean,\n): Record<string, t.Identifier> | Record<string, Array<t.Identifier>> {\n  return getBindingIdentifiers(node, duplicates, true);\n}\n"], "mappings": ";;;;;;;AAAA;;eAGeA,0B;;;AAQf,SAASA,0BAAT,CACEC,IADF,EAEEC,UAFF,EAGsE;EACpE,OAAO,IAAAC,8BAAA,EAAsBF,IAAtB,EAA4BC,UAA5B,EAAwC,IAAxC,CAAP;AACD"}