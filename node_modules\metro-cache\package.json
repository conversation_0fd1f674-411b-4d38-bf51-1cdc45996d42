{"name": "metro-cache", "version": "0.82.4", "description": "🚇 Cache layers for Metro.", "main": "src/index.js", "exports": {".": "./src/index.js", "./package.json": "./package.json", "./private/*": "./src/*.js", "./src": "./src/index.js", "./src/*.js": "./src/*.js", "./src/*": "./src/*.js"}, "repository": {"type": "git", "url": "**************:facebook/metro.git"}, "scripts": {"prepare-release": "test -d build && rm -rf src.real && mv src src.real && mv build src", "cleanup-release": "test ! -e build && mv src build && mv src.real src"}, "dependencies": {"exponential-backoff": "^3.1.1", "flow-enums-runtime": "^0.0.6", "https-proxy-agent": "^7.0.5", "metro-core": "0.82.4"}, "devDependencies": {"metro-memory-fs": "0.82.4"}, "license": "MIT", "engines": {"node": ">=18.18"}}