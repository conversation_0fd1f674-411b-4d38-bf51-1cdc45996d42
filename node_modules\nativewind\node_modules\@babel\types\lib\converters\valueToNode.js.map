{"version": 3, "names": ["valueToNode", "objectToString", "Function", "call", "bind", "Object", "prototype", "toString", "isRegExp", "value", "isPlainObject", "proto", "getPrototypeOf", "undefined", "identifier", "booleanLiteral", "nullLiteral", "stringLiteral", "result", "Number", "isFinite", "numericLiteral", "Math", "abs", "numerator", "isNaN", "binaryExpression", "is", "unaryExpression", "pattern", "source", "flags", "match", "regExpLiteral", "Array", "isArray", "arrayExpression", "map", "props", "key", "keys", "nodeKey", "isValidIdentifier", "push", "objectProperty", "objectExpression", "Error"], "sources": ["../../src/converters/valueToNode.ts"], "sourcesContent": ["import isValidIdentifier from \"../validators/isValidIdentifier\";\nimport {\n  identifier,\n  booleanLiteral,\n  nullLiteral,\n  stringLiteral,\n  numericLiteral,\n  regExpLiteral,\n  arrayExpression,\n  objectProperty,\n  objectExpression,\n  unaryExpression,\n  binaryExpression,\n} from \"../builders/generated\";\nimport type * as t from \"..\";\n\nexport default valueToNode as {\n  (value: undefined): t.Identifier; // TODO: This should return \"void 0\"\n  (value: boolean): t.<PERSON>iteral;\n  (value: null): t.NullLiteral;\n  (value: string): t.StringLiteral;\n  // Infinities and NaN need to use a BinaryExpression; negative values must be wrapped in UnaryExpression\n  (value: number): t.NumericLiteral | t.BinaryExpression | t.UnaryExpression;\n  (value: RegExp): t.RegExpLiteral;\n  (value: ReadonlyArray<unknown>): t.ArrayExpression;\n\n  // this throws with objects that are not plain objects,\n  // or if there are non-valueToNode-able values\n  (value: object): t.ObjectExpression;\n\n  (value: unknown): t.Expression;\n};\n\n// @ts-expect-error: Object.prototype.toString must return a string\nconst objectToString: (value: unknown) => string = Function.call.bind(\n  Object.prototype.toString,\n);\n\nfunction isRegExp(value: unknown): value is RegExp {\n  return objectToString(value) === \"[object RegExp]\";\n}\n\nfunction isPlainObject(value: unknown): value is object {\n  if (\n    typeof value !== \"object\" ||\n    value === null ||\n    Object.prototype.toString.call(value) !== \"[object Object]\"\n  ) {\n    return false;\n  }\n  const proto = Object.getPrototypeOf(value);\n  // Object.prototype's __proto__ is null. Every other class's __proto__.__proto__ is\n  // not null by default. We cannot check if proto === Object.prototype because it\n  // could come from another realm.\n  return proto === null || Object.getPrototypeOf(proto) === null;\n}\n\nfunction valueToNode(value: unknown): t.Expression {\n  // undefined\n  if (value === undefined) {\n    return identifier(\"undefined\");\n  }\n\n  // boolean\n  if (value === true || value === false) {\n    return booleanLiteral(value);\n  }\n\n  // null\n  if (value === null) {\n    return nullLiteral();\n  }\n\n  // strings\n  if (typeof value === \"string\") {\n    return stringLiteral(value);\n  }\n\n  // numbers\n  if (typeof value === \"number\") {\n    let result;\n    if (Number.isFinite(value)) {\n      result = numericLiteral(Math.abs(value));\n    } else {\n      let numerator;\n      if (Number.isNaN(value)) {\n        // NaN\n        numerator = numericLiteral(0);\n      } else {\n        // Infinity / -Infinity\n        numerator = numericLiteral(1);\n      }\n\n      result = binaryExpression(\"/\", numerator, numericLiteral(0));\n    }\n\n    if (value < 0 || Object.is(value, -0)) {\n      result = unaryExpression(\"-\", result);\n    }\n\n    return result;\n  }\n\n  // regexes\n  if (isRegExp(value)) {\n    const pattern = value.source;\n    const flags = value.toString().match(/\\/([a-z]+|)$/)[1];\n    return regExpLiteral(pattern, flags);\n  }\n\n  // array\n  if (Array.isArray(value)) {\n    return arrayExpression(value.map(valueToNode));\n  }\n\n  // object\n  if (isPlainObject(value)) {\n    const props = [];\n    for (const key of Object.keys(value)) {\n      let nodeKey;\n      if (isValidIdentifier(key)) {\n        nodeKey = identifier(key);\n      } else {\n        nodeKey = stringLiteral(key);\n      }\n      props.push(\n        objectProperty(\n          nodeKey,\n          valueToNode(\n            // @ts-expect-error key must present in value\n            value[key],\n          ),\n        ),\n      );\n    }\n    return objectExpression(props);\n  }\n\n  throw new Error(\"don't know how to turn this value into a node\");\n}\n"], "mappings": ";;;;;;;AAAA;;AACA;;eAeeA,W;;AAkBf,MAAMC,cAA0C,GAAGC,QAAQ,CAACC,IAAT,CAAcC,IAAd,CACjDC,MAAM,CAACC,SAAP,CAAiBC,QADgC,CAAnD;;AAIA,SAASC,QAAT,CAAkBC,KAAlB,EAAmD;EACjD,OAAOR,cAAc,CAACQ,KAAD,CAAd,KAA0B,iBAAjC;AACD;;AAED,SAASC,aAAT,CAAuBD,KAAvB,EAAwD;EACtD,IACE,OAAOA,KAAP,KAAiB,QAAjB,IACAA,KAAK,KAAK,IADV,IAEAJ,MAAM,CAACC,SAAP,CAAiBC,QAAjB,CAA0BJ,IAA1B,CAA+BM,KAA/B,MAA0C,iBAH5C,EAIE;IACA,OAAO,KAAP;EACD;;EACD,MAAME,KAAK,GAAGN,MAAM,CAACO,cAAP,CAAsBH,KAAtB,CAAd;EAIA,OAAOE,KAAK,KAAK,IAAV,IAAkBN,MAAM,CAACO,cAAP,CAAsBD,KAAtB,MAAiC,IAA1D;AACD;;AAED,SAASX,WAAT,CAAqBS,KAArB,EAAmD;EAEjD,IAAIA,KAAK,KAAKI,SAAd,EAAyB;IACvB,OAAO,IAAAC,qBAAA,EAAW,WAAX,CAAP;EACD;;EAGD,IAAIL,KAAK,KAAK,IAAV,IAAkBA,KAAK,KAAK,KAAhC,EAAuC;IACrC,OAAO,IAAAM,yBAAA,EAAeN,KAAf,CAAP;EACD;;EAGD,IAAIA,KAAK,KAAK,IAAd,EAAoB;IAClB,OAAO,IAAAO,sBAAA,GAAP;EACD;;EAGD,IAAI,OAAOP,KAAP,KAAiB,QAArB,EAA+B;IAC7B,OAAO,IAAAQ,wBAAA,EAAcR,KAAd,CAAP;EACD;;EAGD,IAAI,OAAOA,KAAP,KAAiB,QAArB,EAA+B;IAC7B,IAAIS,MAAJ;;IACA,IAAIC,MAAM,CAACC,QAAP,CAAgBX,KAAhB,CAAJ,EAA4B;MAC1BS,MAAM,GAAG,IAAAG,yBAAA,EAAeC,IAAI,CAACC,GAAL,CAASd,KAAT,CAAf,CAAT;IACD,CAFD,MAEO;MACL,IAAIe,SAAJ;;MACA,IAAIL,MAAM,CAACM,KAAP,CAAahB,KAAb,CAAJ,EAAyB;QAEvBe,SAAS,GAAG,IAAAH,yBAAA,EAAe,CAAf,CAAZ;MACD,CAHD,MAGO;QAELG,SAAS,GAAG,IAAAH,yBAAA,EAAe,CAAf,CAAZ;MACD;;MAEDH,MAAM,GAAG,IAAAQ,2BAAA,EAAiB,GAAjB,EAAsBF,SAAtB,EAAiC,IAAAH,yBAAA,EAAe,CAAf,CAAjC,CAAT;IACD;;IAED,IAAIZ,KAAK,GAAG,CAAR,IAAaJ,MAAM,CAACsB,EAAP,CAAUlB,KAAV,EAAiB,CAAC,CAAlB,CAAjB,EAAuC;MACrCS,MAAM,GAAG,IAAAU,0BAAA,EAAgB,GAAhB,EAAqBV,MAArB,CAAT;IACD;;IAED,OAAOA,MAAP;EACD;;EAGD,IAAIV,QAAQ,CAACC,KAAD,CAAZ,EAAqB;IACnB,MAAMoB,OAAO,GAAGpB,KAAK,CAACqB,MAAtB;IACA,MAAMC,KAAK,GAAGtB,KAAK,CAACF,QAAN,GAAiByB,KAAjB,CAAuB,cAAvB,EAAuC,CAAvC,CAAd;IACA,OAAO,IAAAC,wBAAA,EAAcJ,OAAd,EAAuBE,KAAvB,CAAP;EACD;;EAGD,IAAIG,KAAK,CAACC,OAAN,CAAc1B,KAAd,CAAJ,EAA0B;IACxB,OAAO,IAAA2B,0BAAA,EAAgB3B,KAAK,CAAC4B,GAAN,CAAUrC,WAAV,CAAhB,CAAP;EACD;;EAGD,IAAIU,aAAa,CAACD,KAAD,CAAjB,EAA0B;IACxB,MAAM6B,KAAK,GAAG,EAAd;;IACA,KAAK,MAAMC,GAAX,IAAkBlC,MAAM,CAACmC,IAAP,CAAY/B,KAAZ,CAAlB,EAAsC;MACpC,IAAIgC,OAAJ;;MACA,IAAI,IAAAC,0BAAA,EAAkBH,GAAlB,CAAJ,EAA4B;QAC1BE,OAAO,GAAG,IAAA3B,qBAAA,EAAWyB,GAAX,CAAV;MACD,CAFD,MAEO;QACLE,OAAO,GAAG,IAAAxB,wBAAA,EAAcsB,GAAd,CAAV;MACD;;MACDD,KAAK,CAACK,IAAN,CACE,IAAAC,yBAAA,EACEH,OADF,EAEEzC,WAAW,CAETS,KAAK,CAAC8B,GAAD,CAFI,CAFb,CADF;IASD;;IACD,OAAO,IAAAM,2BAAA,EAAiBP,KAAjB,CAAP;EACD;;EAED,MAAM,IAAIQ,KAAJ,CAAU,+CAAV,CAAN;AACD"}