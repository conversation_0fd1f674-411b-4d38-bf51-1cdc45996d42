export declare const PinwheelData: {
    PinwheelIn: {
        name: string;
        style: {
            0: {
                transform: {
                    rotate: string;
                    scale: number;
                }[];
                opacity: number;
            };
            100: {
                transform: {
                    rotate: string;
                    scale: number;
                }[];
                opacity: number;
            };
        };
        duration: number;
    };
    PinwheelOut: {
        name: string;
        style: {
            0: {
                transform: {
                    rotate: string;
                    scale: number;
                }[];
                opacity: number;
            };
            100: {
                transform: {
                    rotate: string;
                    scale: number;
                }[];
                opacity: number;
            };
        };
        duration: number;
    };
};
export declare const Pinwheel: {
    PinwheelIn: {
        style: string;
        duration: number;
    };
    PinwheelOut: {
        style: string;
        duration: number;
    };
};
//# sourceMappingURL=Pinwheel.web.d.ts.map