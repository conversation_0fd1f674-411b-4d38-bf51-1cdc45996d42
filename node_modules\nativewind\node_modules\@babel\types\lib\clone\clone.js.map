{"version": 3, "names": ["clone", "node", "cloneNode"], "sources": ["../../src/clone/clone.ts"], "sourcesContent": ["import cloneNode from \"./cloneNode\";\nimport type * as t from \"..\";\n\n/**\n * Create a shallow clone of a `node`, including only\n * properties belonging to the node.\n * @deprecated Use t.cloneNode instead.\n */\nexport default function clone<T extends t.Node>(node: T): T {\n  return cloneNode(node, /* deep */ false);\n}\n"], "mappings": ";;;;;;;AAAA;;AAQe,SAASA,KAAT,CAAiCC,IAAjC,EAA6C;EAC1D,OAAO,IAAAC,kBAAA,EAAUD,IAAV,EAA2B,KAA3B,CAAP;AACD"}