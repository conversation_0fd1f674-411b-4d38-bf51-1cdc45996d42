{"version": 3, "names": ["toStatement", "node", "ignore", "isStatement", "mustHaveId", "newType", "isClass", "isFunction", "isAssignmentExpression", "expressionStatement", "id", "Error", "type"], "sources": ["../../src/converters/toStatement.ts"], "sourcesContent": ["import {\n  isStatement,\n  isFunction,\n  isClass,\n  isAssignmentExpression,\n} from \"../validators/generated\";\nimport { expressionStatement } from \"../builders/generated\";\nimport type * as t from \"..\";\n\nexport default toStatement as {\n  (node: t.AssignmentExpression, ignore?: boolean): t.ExpressionStatement;\n\n  <T extends t.Statement>(node: T, ignore: false): T;\n  <T extends t.Statement>(node: T, ignore?: boolean): T | false;\n\n  (node: t.Class, ignore: false): t.ClassDeclaration;\n  (node: t.Class, ignore?: boolean): t.ClassDeclaration | false;\n\n  (node: t.Function, ignore: false): t.FunctionDeclaration;\n  (node: t.Function, ignore?: boolean): t.FunctionDeclaration | false;\n\n  (node: t.Node, ignore: false): t.Statement;\n  (node: t.Node, ignore?: boolean): t.Statement | false;\n};\n\nfunction toStatement(node: t.Node, ignore?: boolean): t.Statement | false {\n  if (isStatement(node)) {\n    return node;\n  }\n\n  let mustHaveId = false;\n  let newType;\n\n  if (isClass(node)) {\n    mustHaveId = true;\n    newType = \"ClassDeclaration\" as const;\n  } else if (isFunction(node)) {\n    mustHaveId = true;\n    newType = \"FunctionDeclaration\" as const;\n  } else if (isAssignmentExpression(node)) {\n    return expressionStatement(node);\n  }\n\n  // @ts-expect-error todo(flow->ts): node.id might be missing\n  if (mustHaveId && !node.id) {\n    newType = false as false;\n  }\n\n  if (!newType) {\n    if (ignore) {\n      return false;\n    } else {\n      throw new Error(`cannot turn ${node.type} to a statement`);\n    }\n  }\n\n  // @ts-expect-error manipulating node.type\n  node.type = newType;\n\n  // @ts-expect-error todo(flow->ts) refactor to avoid type unsafe mutations like reassigning node type above\n  return node;\n}\n"], "mappings": ";;;;;;;AAAA;;AAMA;;eAGeA,W;;;AAgBf,SAASA,WAAT,CAAqBC,IAArB,EAAmCC,MAAnC,EAA0E;EACxE,IAAI,IAAAC,sBAAA,EAAYF,IAAZ,CAAJ,EAAuB;IACrB,OAAOA,IAAP;EACD;;EAED,IAAIG,UAAU,GAAG,KAAjB;EACA,IAAIC,OAAJ;;EAEA,IAAI,IAAAC,kBAAA,EAAQL,IAAR,CAAJ,EAAmB;IACjBG,UAAU,GAAG,IAAb;IACAC,OAAO,GAAG,kBAAV;EACD,CAHD,MAGO,IAAI,IAAAE,qBAAA,EAAWN,IAAX,CAAJ,EAAsB;IAC3BG,UAAU,GAAG,IAAb;IACAC,OAAO,GAAG,qBAAV;EACD,CAHM,MAGA,IAAI,IAAAG,iCAAA,EAAuBP,IAAvB,CAAJ,EAAkC;IACvC,OAAO,IAAAQ,+BAAA,EAAoBR,IAApB,CAAP;EACD;;EAGD,IAAIG,UAAU,IAAI,CAACH,IAAI,CAACS,EAAxB,EAA4B;IAC1BL,OAAO,GAAG,KAAV;EACD;;EAED,IAAI,CAACA,OAAL,EAAc;IACZ,IAAIH,MAAJ,EAAY;MACV,OAAO,KAAP;IACD,CAFD,MAEO;MACL,MAAM,IAAIS,KAAJ,CAAW,eAAcV,IAAI,CAACW,IAAK,iBAAnC,CAAN;IACD;EACF;;EAGDX,IAAI,CAACW,IAAL,GAAYP,OAAZ;EAGA,OAAOJ,IAAP;AACD"}